/**
 * TON区块链转账BOC生成脚本
 * 基于@ton/ton库实现TON转账交易的BOC(Bag of Cells)生成
 * 
 * 功能特性:
 * - ✅ 自动获取钱包余额和序列号(seqno)
 * - ✅ 自动估算交易费用
 * - ✅ 支持命令行参数指定API端点
 * - ✅ 硬编码默认配置作为fallback
 * - ✅ 生成签名的BOC数据
 * - ✅ 可选择仅生成BOC或生成并发送
 * - ✅ 详细的错误处理和日志输出
 */

const { TonClient, WalletContractV4, WalletContractV3R1, WalletContractV3R2, WalletContractV5R1, internal } = require('@ton/ton');
const { mnemonicToPrivateKey } = require('@ton/crypto');

// 硬编码配置 - 请根据实际情况修改
const CONFIG = {
    // TON助记词 - 请替换为你的实际助记词(24个单词)
    MNEMONIC: 'carpet kiss gap undo wasp add rocket payment whale attract air heart file rude mesh odor law throw snake medal hybrid antique season party',
    
    // 接收地址 - 实际的TON接收地址
    RECIPIENT_ADDRESS: 'UQCznyWPTYdCZyHkx4GpbR2b8lsaTYI5HlE5XgPMb-jXT_YS',
    
    // 转账金额 (TON) - 请根据需要修改
    AMOUNT: '0.0001',
    
    // 转账备注 - 可选
    COMMENT: 'Test transfer from script'
};

// 默认API端点配置 - 官方TON主网端点
const DEFAULT_ENDPOINT = 'https://toncenter.com/api/v2/jsonRPC';

/**
 * 解析命令行参数
 */
function parseArguments() {
    const args = process.argv.slice(2);
    const config = {
        endpoint: DEFAULT_ENDPOINT, // 默认API端点
        send: false,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--endpoint':
            case '--api':
                if (i + 1 < args.length) {
                    config.endpoint = args[i + 1];
                    i++;
                }
                break;
            case '--send':
                config.send = true;
                break;
            case '--help':
            case '-h':
                config.help = true;
                break;
        }
    }

    return config;
}

/**
 * 显示帮助信息
 */
function showHelp() {
    console.log(`
TON区块链转账BOC生成脚本

用法:
  node ton_transfer_boc.js [选项]

选项:
  --endpoint <url>    指定TON API端点URL
  --send             生成BOC后立即发送交易
  --help, -h         显示此帮助信息

示例:
  # 仅生成BOC (默认)
  node ton_transfer_boc.js

  # 生成BOC并发送交易
  node ton_transfer_boc.js --send

  # 指定自定义API端点
  node ton_transfer_boc.js --endpoint https://your-ton-api.com/jsonRPC

注意:
  - 请在脚本中修改CONFIG对象的助记词和接收地址
  - 默认使用主网，如需使用测试网请修改脚本中的DEFAULT_ENDPOINT
  - 确保钱包有足够的TON支付交易费用
`);
}

/**
 * 格式化TON金额显示
 */
function formatTON(nanotons) {
    return (Number(nanotons) / 1e9).toFixed(9) + ' TON';
}

/**
 * 尝试不同钱包版本找到匹配的地址
 */
function findCorrectWalletVersion(publicKey, expectedAddress) {
    const walletVersions = [
        { name: 'W5 (V5R1)', contract: WalletContractV5R1 },
        { name: 'V4R2', contract: WalletContractV4 },
        { name: 'V3R2', contract: WalletContractV3R2 },
        { name: 'V3R1', contract: WalletContractV3R1 }
    ];

    for (const version of walletVersions) {
        const wallet = version.contract.create({
            workchain: 0,
            publicKey: publicKey
        });

        const address = wallet.address.toString({
            urlSafe: true,
            bounceable: false
        });

        console.log(`🔍 尝试钱包版本 ${version.name}: ${address}`);

        if (address === expectedAddress) {
            console.log(`✅ 找到匹配的钱包版本: ${version.name}`);
            return { wallet, version: version.name };
        }
    }

    console.log('❌ 未找到匹配的钱包版本');
    return null;
}

/**
 * 创建TON转账BOC
 */
async function createTransferBOC() {
    const args = parseArguments();
    
    if (args.help) {
        showHelp();
        return;
    }

    console.log('🚀 TON转账BOC生成脚本启动');
    console.log(` API端点: ${args.endpoint}`);
    console.log(`💰 转账金额: ${CONFIG.AMOUNT} TON`);
    console.log(`📝 备注: ${CONFIG.COMMENT}`);
    console.log(`📤 接收地址: ${CONFIG.RECIPIENT_ADDRESS}`);
    console.log('');

    try {
        // 1. 初始化TON客户端
        console.log('🔧 初始化TON客户端...');
        const client = new TonClient({
            endpoint: args.endpoint,
        });

        // 2. 从助记词生成密钥对
        console.log('🔑 生成钱包密钥...');
        const keyPair = await mnemonicToPrivateKey(CONFIG.MNEMONIC.split(' '));
        
        // 3. 尝试找到正确的钱包版本
        console.log('🔍 查找正确的钱包版本...');
        const expectedAddress = 'UQCznyWPTYdCZyHkx4GpbR2b8lsaTYI5HlE5XgPMb-jXT_YS';
        const walletResult = findCorrectWalletVersion(keyPair.publicKey, expectedAddress);

        if (!walletResult) {
            throw new Error(`无法找到生成地址 ${expectedAddress} 的钱包版本。请检查助记词是否正确。`);
        }

        const { wallet, version } = walletResult;
        const contract = client.open(wallet);
        const walletAddressRaw = wallet.address.toString();
        const walletAddressUserFriendly = wallet.address.toString({
            urlSafe: true,
            bounceable: false
        });

        console.log(`👛 使用钱包版本: ${version}`);
        console.log(`👛 钱包地址 (Raw): ${walletAddressRaw}`);
        console.log(`👛 钱包地址 (User-friendly): ${walletAddressUserFriendly}`);
        console.log('✅ 钱包地址验证成功！');

        // 4. 获取钱包信息
        console.log('📊 获取钱包信息...');
        const balance = await contract.getBalance();
        const seqno = await contract.getSeqno();
        
        console.log(`💰 当前余额: ${formatTON(balance)}`);
        console.log(`🔢 序列号(seqno): ${seqno}`);

        // 5. 检查余额是否足够
        const transferAmount = BigInt(parseFloat(CONFIG.AMOUNT) * 1e9);
        const estimatedFee = BigInt(0.01 * 1e9); // 预估手续费 0.01 TON
        const totalRequired = transferAmount + estimatedFee;

        if (balance < totalRequired) {
            throw new Error(`余额不足! 需要: ${formatTON(totalRequired)}, 当前: ${formatTON(balance)}`);
        }

        // 6. 创建转账消息
        console.log('📝 创建转账消息...');
        const transfer = contract.createTransfer({
            seqno,
            secretKey: keyPair.secretKey,
            messages: [
                internal({
                    to: CONFIG.RECIPIENT_ADDRESS,
                    value: transferAmount,
                    body: CONFIG.COMMENT,
                    bounce: false
                })
            ]
        });

        // 7. 生成BOC
        console.log('🔨 生成BOC数据...');
        const bocCell = transfer;
        const bocBase64 = bocCell.toBoc().toString('base64');
        const bocHex = bocCell.toBoc().toString('hex');

        console.log('');
        console.log('✅ BOC生成成功!');
        console.log('📦 BOC信息:');
        console.log(`   Base64: ${bocBase64}`);
        console.log(`   Hex: ${bocHex}`);
        console.log(`   大小: ${bocCell.toBoc().length} bytes`);

        // 8. 可选发送交易
        if (args.send) {
            console.log('');
            console.log('📡 发送交易到网络...');
            
            try {
                await contract.send(transfer);
                console.log('✅ 交易发送成功!');
                console.log(`🔍 请在区块链浏览器中查看交易状态`);
                console.log(`   钱包地址: ${walletAddressUserFriendly}`);
                
                // 等待一段时间后检查新的seqno
                console.log('⏳ 等待交易确认...');
                setTimeout(async () => {
                    try {
                        const newSeqno = await contract.getSeqno();
                        if (newSeqno > seqno) {
                            console.log(`✅ 交易已确认! 新序列号: ${newSeqno}`);
                        } else {
                            console.log('⏳ 交易仍在处理中...');
                        }
                    } catch (error) {
                        console.log('⚠️  无法检查交易状态:', error.message);
                    }
                }, 10000);
                
            } catch (sendError) {
                console.error('❌ 发送交易失败:', sendError.message);
                console.log('💡 BOC已生成，你可以手动提交到网络');
            }
        } else {
            console.log('');
            console.log('💡 仅生成BOC模式 - 交易未发送');
            console.log('   如需发送交易，请使用 --send 参数');
        }

        console.log('');
        console.log('🎉 脚本执行完成!');

    } catch (error) {
        console.error('');
        console.error('❌ 错误:', error.message);
        console.error('');
        
        // 提供故障排除建议
        if (error.message.includes('Invalid mnemonic')) {
            console.error('💡 故障排除建议:');
            console.error('   - 检查助记词是否正确 (应为24个单词)');
            console.error('   - 确保助记词单词之间用空格分隔');
        } else if (error.message.includes('余额不足')) {
            console.error('💡 故障排除建议:');
            console.error('   - 向钱包充值更多TON');
            console.error('   - 减少转账金额');
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            console.error('💡 故障排除建议:');
            console.error('   - 检查网络连接');
            console.error('   - 尝试使用不同的API端点');
            console.error('   - 确认API端点URL正确');
        }
        
        process.exit(1);
    }
}

// 运行脚本
if (require.main === module) {
    createTransferBOC().catch(console.error);
}

module.exports = { createTransferBOC, CONFIG };
