/**
 * 检查委托合约的实际状态
 */

const { ethers } = require('ethers');

// 委托合约地址
const CONTRACT_ADDRESS = '******************************************';

// RPC 端点
const RPC_URL = 'https://cold-dawn-pool.matic.quiknode.pro/b3b7d9e315efb44a0165c8ebab4edc8668a2bb68/';

// 预期的 ABI
const EXPECTED_ABI = [
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function setValue(uint256 _value) external",
    "function value() external view returns (uint256)",
    "function nonce() external view returns (uint256)",
    "function getNonce() external view returns (uint256)",
    "function getBalance() external view returns (uint256)"
];

async function checkContract() {
    console.log('🔍 检查委托合约状态');
    console.log('合约地址:', CONTRACT_ADDRESS);
    console.log('RPC 端点:', RPC_URL);
    
    try {
        // 初始化 provider
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        
        // 检查网络连接
        const network = await provider.getNetwork();
        console.log('\n=== 网络信息 ===');
        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        
        // 检查合约是否存在
        console.log('\n=== 合约存在性检查 ===');
        const code = await provider.getCode(CONTRACT_ADDRESS);
        
        if (code === '0x') {
            console.log('❌ 合约不存在或未部署');
            return;
        }
        
        console.log('✅ 合约存在');
        console.log('字节码长度:', code.length);
        console.log('字节码前缀:', code.substring(0, 20) + '...');
        
        // 检查合约余额
        const balance = await provider.getBalance(CONTRACT_ADDRESS);
        console.log('合约余额:', ethers.formatEther(balance), 'MATIC');
        
        // 尝试创建合约实例并测试函数
        console.log('\n=== 合约函数测试 ===');
        const contract = new ethers.Contract(CONTRACT_ADDRESS, EXPECTED_ABI, provider);
        
        // 测试只读函数
        try {
            console.log('测试 value() 函数...');
            const value = await contract.value();
            console.log('✅ value():', value.toString());
        } catch (error) {
            console.log('❌ value() 失败:', error.message);
        }
        
        try {
            console.log('测试 nonce() 函数...');
            const nonce = await contract.nonce();
            console.log('✅ nonce():', nonce.toString());
        } catch (error) {
            console.log('❌ nonce() 失败:', error.message);
        }
        
        try {
            console.log('测试 getNonce() 函数...');
            const getNonce = await contract.getNonce();
            console.log('✅ getNonce():', getNonce.toString());
        } catch (error) {
            console.log('❌ getNonce() 失败:', error.message);
        }
        
        try {
            console.log('测试 getBalance() 函数...');
            const getBalance = await contract.getBalance();
            console.log('✅ getBalance():', ethers.formatEther(getBalance), 'MATIC');
        } catch (error) {
            console.log('❌ getBalance() 失败:', error.message);
        }
        
        // 检查 execute 函数的存在性（不调用，只检查）
        console.log('\n=== 函数签名检查 ===');
        try {
            // 尝试编码一个简单的 execute 调用来验证函数签名
            const calls = [
                [ethers.ZeroAddress, 0, '0x']
            ];
            
            const data = contract.interface.encodeFunctionData('execute', [calls]);
            console.log('✅ execute() 函数签名正确');
            console.log('函数选择器:', data.substring(0, 10));
        } catch (error) {
            console.log('❌ execute() 函数签名错误:', error.message);
        }
        
        // 分析可能的问题
        console.log('\n=== 问题分析 ===');
        console.log('1. 合约存在且有字节码 ✅');
        console.log('2. 检查上述函数测试结果');
        console.log('3. 如果函数调用失败，可能是：');
        console.log('   - ABI 定义与实际合约不匹配');
        console.log('   - 合约字节码有问题');
        console.log('   - 合约缺少预期的函数');
        
        // 尝试反编译合约（获取函数选择器）
        console.log('\n=== 合约函数选择器分析 ===');
        
        // 计算预期的函数选择器
        const iface = new ethers.Interface(EXPECTED_ABI);
        console.log('预期的函数选择器:');
        for (const fragment of iface.fragments) {
            if (fragment.type === 'function') {
                console.log(`  ${fragment.name}: ${iface.getFunction(fragment.name).selector}`);
            }
        }
        
    } catch (error) {
        console.error('检查合约时发生错误:', error.message);
    }
}

// 运行检查
if (require.main === module) {
    checkContract().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
