/**
 * 部署正确的委托合约
 * 
 * 使用简单但正确的合约实现
 */

const { ethers } = require('ethers');

// 正确的委托合约 ABI
const DELEGATION_ABI = [
    "constructor()",
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function nonce() external view returns (uint256)",
    "function getBalance() external view returns (uint256)",
    "event BatchExecuted(address indexed executor, uint256 callCount)"
];

// 简单但正确的委托合约字节码
// 这是一个最小化的实现，只包含必要的函数
const DELEGATION_BYTECODE = "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";

/**
 * 解析命令行参数
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        privateKey: null,
        rpcUrl: null,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--private-key':
            case '--key':
                if (i + 1 < args.length) {
                    result.privateKey = args[i + 1];
                    i++;
                }
                break;
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--help':
            case '-h':
                result.help = true;
                break;
        }
    }

    return result;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('正确的委托合约部署脚本使用说明:');
    console.log('');
    console.log('基本用法:');
    console.log('  node deploy_correct_delegation.js --private-key YOUR_PRIVATE_KEY [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --private-key <key>    部署者私钥 (必需)');
    console.log('  --rpc <url>           RPC 端点地址 (默认: QuickNode)');
    console.log('  --help, -h            显示此帮助信息');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }
    
    const cleanKey = privateKey.replace(/^0x/, '');
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }
    
    return '0x' + cleanKey;
}

/**
 * 部署正确的委托合约
 */
async function deployCorrectDelegationContract(signer) {
    console.log('\n=== 开始部署正确的委托合约 ===');
    
    try {
        // 创建合约工厂
        const contractFactory = new ethers.ContractFactory(
            DELEGATION_ABI,
            DELEGATION_BYTECODE,
            signer
        );
        
        // 估算 gas
        console.log('正在估算部署 gas...');
        const deployTx = contractFactory.getDeployTransaction();
        const gasEstimate = await signer.provider.estimateGas(deployTx);
        
        console.log('估算 Gas 使用量:', gasEstimate.toString());
        
        // 获取 gas 价格
        const feeData = await signer.provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        
        const estimatedCost = gasEstimate * gasPrice;
        console.log('预估部署成本:', ethers.formatEther(estimatedCost), 'MATIC');
        
        // 检查余额
        const balance = await signer.provider.getBalance(signer.address);
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        if (balance < estimatedCost) {
            throw new Error(`余额不足！需要至少 ${ethers.formatEther(estimatedCost)} MATIC`);
        }
        
        // 部署合约
        console.log('正在部署合约...');
        const contract = await contractFactory.deploy({
            gasLimit: gasEstimate + (gasEstimate * BigInt(20) / BigInt(100)) // 增加 20% 缓冲
        });
        
        console.log('✅ 合约部署交易已发送');
        console.log('交易哈希:', contract.deploymentTransaction().hash);
        console.log('合约地址:', await contract.getAddress());
        
        // 等待部署确认
        console.log('正在等待部署确认...');
        await contract.waitForDeployment();
        
        console.log('✅ 合约部署成功！');
        
        // 获取部署收据
        const receipt = await contract.deploymentTransaction().wait();
        console.log('部署区块号:', receipt.blockNumber);
        console.log('实际 Gas 使用量:', receipt.gasUsed.toString());
        console.log('实际部署成本:', ethers.formatEther(receipt.gasUsed * receipt.gasPrice), 'MATIC');
        
        // 验证合约功能
        console.log('\n=== 验证合约功能 ===');
        try {
            const initialNonce = await contract.nonce();
            console.log('✅ nonce() 函数正常:', initialNonce.toString());
            
            const contractBalance = await contract.getBalance();
            console.log('✅ getBalance() 函数正常:', ethers.formatEther(contractBalance), 'MATIC');
            
            // 测试 execute 函数编码
            const testCalls = [[ethers.ZeroAddress, 0, '0x']];
            const executeData = contract.interface.encodeFunctionData('execute', [testCalls]);
            console.log('✅ execute() 函数签名正确:', executeData.substring(0, 10));
            
        } catch (error) {
            console.log('⚠️  合约功能验证失败:', error.message);
        }
        
        return {
            address: await contract.getAddress(),
            contract: contract,
            receipt: receipt
        };
        
    } catch (error) {
        console.error('部署合约失败:', error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 正确的委托合约部署脚本启动');
    
    // 解析命令行参数
    const args = parseCommandLineArgs();
    
    if (args.help) {
        showUsage();
        return;
    }
    
    if (!args.privateKey) {
        console.error('错误：必须提供私钥');
        console.error('使用 --help 查看使用说明');
        return;
    }
    
    try {
        // 验证和设置参数
        const privateKey = validatePrivateKey(args.privateKey);
        const rpcUrl = args.rpcUrl || 'https://cold-dawn-pool.matic.quiknode.pro/b3b7d9e315efb44a0165c8ebab4edc8668a2bb68/';
        
        console.log('\n=== 配置信息 ===');
        console.log('RPC 地址:', rpcUrl);
        
        // 初始化 provider 和 signer
        console.log('\n=== 初始化连接 ===');
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);
        
        console.log('部署者地址:', signer.address);
        
        // 检查网络信息
        const network = await provider.getNetwork();
        const balance = await provider.getBalance(signer.address);
        
        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        // 部署合约
        const deployment = await deployCorrectDelegationContract(signer);
        
        console.log('\n=== 部署完成 ===');
        console.log('✅ 正确的委托合约已成功部署到 Polygon 网络');
        console.log('📍 合约地址:', deployment.address);
        console.log('🔗 区块浏览器:', `https://polygonscan.com/address/${deployment.address}`);
        console.log('');
        console.log('💡 使用提示:');
        console.log(`   更新 EIP-7702 测试脚本中的合约地址:`);
        console.log(`   DELEGATION_CONTRACT: '${deployment.address}'`);
        console.log('');
        console.log('🔧 下一步:');
        console.log('1. 更新 eip7702_test.js 中的 DELEGATION_CONTRACT 地址');
        console.log('2. 重新运行 EIP-7702 测试');
        
    } catch (error) {
        console.error('部署失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
