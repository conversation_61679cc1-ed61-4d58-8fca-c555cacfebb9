// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title EIP-7702 委托合约示例
 * @dev 用于 EIP-7702 Set Code Transaction 的简单委托实现
 * @notice 此合约允许 EOA 通过 EIP-7702 获得智能合约功能
 */
contract DelegationContract {
    
    // 用于签名验证的 nonce
    uint256 public nonce;
    
    // 调用结构体
    struct Call {
        address to;      // 目标地址
        uint256 value;   // 发送的 ETH 数量
        bytes data;      // 调用数据
    }
    
    // 事件定义
    event BatchExecuted(address indexed executor, uint256 callCount);
    event SponsoredExecution(address indexed sponsor, address indexed beneficiary, uint256 callCount);
    event NonceIncremented(uint256 newNonce);
    
    /**
     * @dev 直接执行批量调用 (非赞助模式)
     * @param calls 要执行的调用数组
     * 
     * 注意：在 EIP-7702 中，msg.sender 将是委托的 EOA 地址
     */
    function execute(Call[] calldata calls) external payable {
        require(calls.length > 0, "No calls provided");
        
        // 执行所有调用
        for (uint256 i = 0; i < calls.length; i++) {
            Call calldata call = calls[i];
            
            // 执行调用
            (bool success, bytes memory result) = call.to.call{value: call.value}(call.data);
            
            if (!success) {
                // 如果调用失败，返回错误信息
                if (result.length > 0) {
                    assembly {
                        let returndata_size := mload(result)
                        revert(add(32, result), returndata_size)
                    }
                } else {
                    revert("Call failed");
                }
            }
        }
        
        emit BatchExecuted(msg.sender, calls.length);
    }
    
    /**
     * @dev 赞助执行批量调用 (需要签名验证)
     * @param calls 要执行的调用数组
     * @param signature EOA 对调用的授权签名
     * 
     * 注意：在赞助模式下，msg.sender 是赞助方，但执行的是 EOA 的操作
     */
    function execute(Call[] calldata calls, bytes calldata signature) external payable {
        require(calls.length > 0, "No calls provided");
        require(signature.length == 65, "Invalid signature length");
        
        // 验证签名
        bytes32 digest = _getDigest(calls);
        address signer = _recoverSigner(digest, signature);
        
        // 在 EIP-7702 中，address(this) 实际上是委托的 EOA 地址
        require(signer == address(this), "Invalid signature");
        
        // 增加 nonce 防止重放攻击
        nonce++;
        
        // 执行所有调用
        for (uint256 i = 0; i < calls.length; i++) {
            Call calldata call = calls[i];
            
            // 执行调用
            (bool success, bytes memory result) = call.to.call{value: call.value}(call.data);
            
            if (!success) {
                // 如果调用失败，返回错误信息
                if (result.length > 0) {
                    assembly {
                        let returndata_size := mload(result)
                        revert(add(32, result), returndata_size)
                    }
                } else {
                    revert("Call failed");
                }
            }
        }
        
        emit SponsoredExecution(msg.sender, address(this), calls.length);
        emit NonceIncremented(nonce);
    }
    
    /**
     * @dev 获取当前 nonce
     * @return 当前的 nonce 值
     */
    function getNonce() external view returns (uint256) {
        return nonce;
    }
    
    /**
     * @dev 生成需要签名的摘要
     * @param calls 调用数组
     * @return 签名摘要
     */
    function _getDigest(Call[] calldata calls) internal view returns (bytes32) {
        // 编码所有调用
        bytes memory encodedCalls = "";
        for (uint256 i = 0; i < calls.length; i++) {
            encodedCalls = abi.encodePacked(
                encodedCalls,
                calls[i].to,
                calls[i].value,
                calls[i].data
            );
        }
        
        // 创建摘要
        return keccak256(abi.encodePacked(nonce, encodedCalls));
    }
    
    /**
     * @dev 从签名中恢复签名者地址
     * @param digest 签名的摘要
     * @param signature 签名数据
     * @return 签名者地址
     */
    function _recoverSigner(bytes32 digest, bytes calldata signature) internal pure returns (address) {
        // 添加以太坊签名前缀
        bytes32 ethSignedMessageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", digest)
        );
        
        // 解析签名
        bytes32 r;
        bytes32 s;
        uint8 v;
        
        assembly {
            r := calldataload(signature.offset)
            s := calldataload(add(signature.offset, 0x20))
            v := byte(0, calldataload(add(signature.offset, 0x40)))
        }
        
        // 恢复地址
        return ecrecover(ethSignedMessageHash, v, r, s);
    }
    
    /**
     * @dev 允许合约接收 ETH
     */
    receive() external payable {}
    
    /**
     * @dev 回退函数
     */
    fallback() external payable {}
    
    /**
     * @dev 获取合约余额
     * @return 合约的 ETH 余额
     */
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev 紧急提取函数 (仅限合约所有者)
     * 注意：在 EIP-7702 中，"所有者" 是委托的 EOA
     */
    function emergencyWithdraw() external {
        // 在 EIP-7702 中，只有委托的 EOA 可以调用
        require(msg.sender == address(this), "Only delegated EOA can withdraw");
        
        uint256 balance = address(this).balance;
        require(balance > 0, "No balance to withdraw");
        
        (bool success, ) = payable(msg.sender).call{value: balance}("");
        require(success, "Withdrawal failed");
    }
}
