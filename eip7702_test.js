/**
 * EIP-7702 交易测试脚本 - Polygon 主网版本
 *
 * 功能说明：
 * - 构造和发送 EIP-7702 Set Code Transaction (交易类型 0x04)
 * - 支持授权列表 (authorization list) 构造
 * - 支持非赞助和赞助交易模式
 * - 自动检测链 ID 和估算 gas 费用
 * - 完整的交易签名和发送流程
 *
 * 注意：EIP-7702 可能还未在 Polygon 主网激活，建议先在支持的测试网络测试
 *
 * 使用方法：
 * node eip7702_test.js --private-key YOUR_PRIVATE_KEY --rpc https://polygon-rpc.com
 * node eip7702_test.js --private-key YOUR_PRIVATE_KEY --sponsor-key SPONSOR_KEY --rpc https://polygon-rpc.com
 */

const { ethers } = require('ethers');

// 默认配置
const DEFAULT_CONFIG = {
    // Polygon 主网 RPC 地址
    RPC_URL: 'https://polygon.blockpi.network/v1/rpc/3661d2d3e65b37a63f1dc8e5930f43df97385c71',
    // 预期的链 ID
    CHAIN_ID: 137,
    // 示例委托合约地址 (需要部署到 Polygon 主网)
    DELEGATION_CONTRACT: '******************************************', // 需要替换为实际地址
    // 测试接收地址
    RECIPIENT_ADDRESS: '******************************************'
};

// 简单的委托合约 ABI
const DELEGATION_CONTRACT_ABI = [
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function execute((address,uint256,bytes)[] calls, bytes signature) external payable",
    "function nonce() external view returns (uint256)"
];

/**
 * 解析命令行参数
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        privateKey: "fa1931ab85f8605ae590e60e88aae8086f5aea18149535fc75d52ff5cbae78a2",
        sponsorKey: null,
        rpcUrl: null,
        delegationContract: null,
        recipient: null,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--private-key':
            case '--key':
                if (i + 1 < args.length) {
                    result.privateKey = args[i + 1];
                    i++;
                }
                break;
            case '--sponsor-key':
                if (i + 1 < args.length) {
                    result.sponsorKey = args[i + 1];
                    i++;
                }
                break;
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--contract':
                if (i + 1 < args.length) {
                    result.delegationContract = args[i + 1];
                    i++;
                }
                break;
            case '--recipient':
                if (i + 1 < args.length) {
                    result.recipient = args[i + 1];
                    i++;
                }
                break;
            case '--help':
            case '-h':
                result.help = true;
                break;
        }
    }

    return result;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('EIP-7702 交易测试脚本使用说明:');
    console.log('');
    console.log('基本用法:');
    console.log('  node eip7702_test.js --private-key YOUR_PRIVATE_KEY [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --private-key <key>    发送方私钥 (必需)');
    console.log('  --sponsor-key <key>    赞助方私钥 (可选，用于赞助交易)');
    console.log('  --rpc <url>           RPC 端点地址 (默认: Polygon 主网)');
    console.log('  --contract <address>   委托合约地址 (可选)');
    console.log('  --recipient <address>  接收方地址 (可选)');
    console.log('  --help, -h            显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  # 基本 EIP-7702 交易');
    console.log('  node eip7702_test.js --private-key 0x123... --rpc https://polygon-rpc.com');
    console.log('');
    console.log('  # 赞助交易模式');
    console.log('  node eip7702_test.js --private-key 0x123... --sponsor-key 0x456... --rpc https://polygon-rpc.com');
    console.log('');
    console.log('注意事项:');
    console.log('  - EIP-7702 可能还未在 Polygon 主网激活');
    console.log('  - 建议先在支持的测试网络进行测试');
    console.log('  - 确保账户有足够的 MATIC 用于 gas 费用');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }

    const cleanKey = privateKey.replace(/^0x/, '');
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }

    return '0x' + cleanKey;
}

/**
 * 验证以太坊地址格式
 */
function validateAddress(address) {
    if (!address) {
        throw new Error('地址不能为空');
    }

    if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
        throw new Error('地址格式无效');
    }

    return address.toLowerCase();
}

/**
 * 检查网络是否支持 EIP-7702
 */
async function checkEIP7702Support(provider) {
    console.log('正在检查网络对 EIP-7702 的支持...');

    try {
        // 尝试创建一个简单的授权来测试支持
        const testWallet = ethers.Wallet.createRandom();
        const testAuth = await testWallet.authorize({
            address: ethers.ZeroAddress,
            nonce: 0
        });

        console.log('✅ ethers.js 支持 EIP-7702 授权创建');
        return true;
    } catch (error) {
        console.log('❌ ethers.js 版本可能不支持 EIP-7702:', error.message);
        return false;
    }
}

/**
 * 检查账户的委托状态
 */
async function checkDelegationStatus(provider, address) {
    console.log(`\n=== 检查委托状态: ${address} ===`);

    try {
        const code = await provider.getCode(address);

        if (code === '0x') {
            console.log('❌ 未发现委托，账户为普通 EOA');
            return null;
        }

        // 检查是否为 EIP-7702 委托 (以 0xef0100 开头)
        if (code.startsWith('0xef0100')) {
            const delegatedAddress = '0x' + code.slice(8);
            console.log('✅ 发现 EIP-7702 委托');
            console.log('📍 委托目标:', delegatedAddress);
            console.log('📝 完整委托代码:', code);
            return delegatedAddress;
        } else {
            console.log('❓ 账户有代码但非 EIP-7702 委托:', code.slice(0, 20) + '...');
            return null;
        }
    } catch (error) {
        console.error('检查委托状态时出错:', error.message);
        return null;
    }
}

/**
 * 创建授权签名
 */
async function createAuthorization(signer, contractAddress, nonce, chainId) {
    console.log(`正在创建授权签名 (nonce: ${nonce}, chainId: ${chainId})...`);

    try {
        const auth = await signer.authorize({
            address: contractAddress,
            nonce: nonce,
            chainId: chainId
        });

        console.log('✅ 授权签名创建成功');
        console.log('授权详情:', {
            address: auth.address,
            nonce: auth.nonce,
            chainId: auth.chainId,
            yParity: auth.yParity,
            r: auth.r,
            s: auth.s
        });

        return auth;
    } catch (error) {
        console.error('创建授权签名失败:', error.message);
        throw error;
    }
}

/**
 * 发送非赞助 EIP-7702 交易
 */
async function sendNonSponsoredTransaction(signer, contractAddress, recipientAddress, chainId) {
    console.log('\n=== 发送非赞助 EIP-7702 交易 ===');

    try {
        // 获取当前 nonce
        const currentNonce = await signer.getNonce();
        console.log('当前 nonce:', currentNonce);

        // 创建授权 (使用 currentNonce + 1，因为是同一账户发送)
        const auth = await createAuthorization(signer, contractAddress, currentNonce + 1, chainId);

        // 准备批量调用数据 - 发送少量 ETH 到两个地址
        const calls = [
            [ethers.ZeroAddress, ethers.parseEther('0.001'), '0x'], // 销毁 0.001 ETH
            [recipientAddress, ethers.parseEther('0.002'), '0x']    // 发送 0.002 ETH
        ];

        console.log('准备执行的调用:');
        calls.forEach((call, index) => {
            console.log(`  调用 ${index + 1}: 发送 ${ethers.formatEther(call[1])} ETH 到 ${call[0]}`);
        });

        // 创建委托合约实例 (注意：指向 EOA 地址，不是实现合约地址)
        const delegatedContract = new ethers.Contract(
            signer.address,
            DELEGATION_CONTRACT_ABI,
            signer
        );

        // 估算 gas
        console.log('正在估算 gas...');
        const gasEstimate = await delegatedContract['execute((address,uint256,bytes)[])'].estimateGas(
            calls,
            {
                type: 4,
                authorizationList: [auth]
            }
        );

        console.log('估算的 gas limit:', gasEstimate.toString());

        // 发送交易
        console.log('正在发送交易...');
        const tx = await delegatedContract['execute((address,uint256,bytes)[])'](
            calls,
            {
                type: 4,
                authorizationList: [auth],
                gasLimit: gasEstimate + (gasEstimate * BigInt(20) / BigInt(100)) // 增加 20% 缓冲
            }
        );

        console.log('✅ 交易已发送');
        console.log('交易哈希:', tx.hash);
        console.log('交易类型:', tx.type);

        // 等待交易确认
        console.log('正在等待交易确认...');
        const receipt = await tx.wait();

        console.log('✅ 交易确认成功');
        console.log('区块号:', receipt.blockNumber);
        console.log('Gas 使用量:', receipt.gasUsed.toString());
        console.log('交易状态:', receipt.status === 1 ? '成功' : '失败');

        return receipt;

    } catch (error) {
        console.error('发送非赞助交易失败:', error.message);
        throw error;
    }
}

/**
 * 为赞助调用创建签名
 */
async function createSignatureForCalls(signer, calls, contractNonce) {
    console.log('正在为赞助调用创建签名...');

    try {
        // 编码调用数据
        let encodedCalls = '0x';
        for (const call of calls) {
            const [to, value, data] = call;
            encodedCalls += ethers.solidityPacked(
                ['address', 'uint256', 'bytes'],
                [to, value, data]
            ).slice(2);
        }

        // 创建需要签名的摘要
        const digest = ethers.keccak256(
            ethers.solidityPacked(
                ['uint256', 'bytes'],
                [contractNonce, encodedCalls]
            )
        );

        console.log('签名摘要:', digest);

        // 使用 EOA 私钥签名
        const signature = await signer.signMessage(ethers.getBytes(digest));

        console.log('✅ 签名创建成功');
        return signature;

    } catch (error) {
        console.error('创建签名失败:', error.message);
        throw error;
    }
}

/**
 * 发送赞助 EIP-7702 交易
 */
async function sendSponsoredTransaction(eoaSigner, sponsorSigner, contractAddress, recipientAddress) {
    console.log('\n=== 发送赞助 EIP-7702 交易 ===');

    try {
        // 准备调用数据 - 发送 ETH
        const calls = [
            [recipientAddress, ethers.parseEther('0.001'), '0x'], // 发送 0.001 ETH
            [ethers.ZeroAddress, ethers.parseEther('0.0005'), '0x'] // 销毁 0.0005 ETH
        ];

        console.log('准备执行的赞助调用:');
        calls.forEach((call, index) => {
            console.log(`  调用 ${index + 1}: 发送 ${ethers.formatEther(call[1])} ETH 到 ${call[0]}`);
        });

        // 创建委托合约实例 (使用赞助方作为发送者)
        const delegatedContract = new ethers.Contract(
            eoaSigner.address,
            DELEGATION_CONTRACT_ABI,
            sponsorSigner
        );

        // 获取合约 nonce 并创建签名
        const contractNonce = await delegatedContract.nonce();
        console.log('合约 nonce:', contractNonce.toString());

        const signature = await createSignatureForCalls(eoaSigner, calls, contractNonce);

        // 估算 gas
        console.log('正在估算 gas...');
        const gasEstimate = await delegatedContract['execute((address,uint256,bytes)[],bytes)'].estimateGas(
            calls,
            signature
        );

        console.log('估算的 gas limit:', gasEstimate.toString());

        // 发送赞助交易
        console.log('正在发送赞助交易...');
        const tx = await delegatedContract['execute((address,uint256,bytes)[],bytes)'](
            calls,
            signature,
            {
                gasLimit: gasEstimate + (gasEstimate * BigInt(20) / BigInt(100)) // 增加 20% 缓冲
            }
        );

        console.log('✅ 赞助交易已发送');
        console.log('交易哈希:', tx.hash);
        console.log('赞助方:', sponsorSigner.address);
        console.log('受益方:', eoaSigner.address);

        // 等待交易确认
        console.log('正在等待交易确认...');
        const receipt = await tx.wait();

        console.log('✅ 赞助交易确认成功');
        console.log('区块号:', receipt.blockNumber);
        console.log('Gas 使用量:', receipt.gasUsed.toString());
        console.log('交易状态:', receipt.status === 1 ? '成功' : '失败');

        return receipt;

    } catch (error) {
        console.error('发送赞助交易失败:', error.message);
        throw error;
    }
}

/**
 * 撤销委托
 */
async function revokeDelegation(signer, chainId) {
    console.log('\n=== 撤销委托 ===');

    try {
        const currentNonce = await signer.getNonce();
        console.log('当前 nonce:', currentNonce);

        // 创建撤销授权 (地址设为零地址)
        const revokeAuth = await createAuthorization(signer, ethers.ZeroAddress, currentNonce + 1, chainId);

        console.log('正在发送撤销交易...');
        const tx = await signer.sendTransaction({
            type: 4,
            to: signer.address,
            authorizationList: [revokeAuth],
            value: 0
        });

        console.log('✅ 撤销交易已发送');
        console.log('交易哈希:', tx.hash);

        // 等待交易确认
        console.log('正在等待交易确认...');
        const receipt = await tx.wait();

        console.log('✅ 委托撤销成功');
        console.log('区块号:', receipt.blockNumber);
        console.log('交易状态:', receipt.status === 1 ? '成功' : '失败');

        return receipt;

    } catch (error) {
        console.error('撤销委托失败:', error.message);
        throw error;
    }
}

/**
 * 统一错误处理
 */
function handleError(error, context = '') {
    console.error(`${context}发生错误:`, error.message);

    if (error.code) {
        switch (error.code) {
            case 'NETWORK_ERROR':
                console.error('网络连接错误，请检查 RPC 地址是否正确');
                break;
            case 'INVALID_ARGUMENT':
                console.error('参数错误，请检查私钥和地址格式');
                break;
            case 'INSUFFICIENT_FUNDS':
                console.error('余额不足，请检查账户余额');
                break;
            case 'NONCE_EXPIRED':
                console.error('Nonce 过期，请重试');
                break;
            case 'UNSUPPORTED_OPERATION':
                console.error('不支持的操作，可能网络不支持 EIP-7702');
                break;
            default:
                console.error('未知错误代码:', error.code);
        }
    }

    if (error.message.includes('insufficient funds')) {
        console.error('余额不足，请检查账户 MATIC 余额');
    } else if (error.message.includes('nonce')) {
        console.error('Nonce 相关错误，请检查交易顺序');
    } else if (error.message.includes('7702') || error.message.includes('authorization')) {
        console.error('EIP-7702 相关错误，可能网络不支持此功能');
    }

    process.exit(1);
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 EIP-7702 交易测试脚本启动');
    console.log('⚠️  注意：EIP-7702 可能还未在 Polygon 主网激活');

    // 解析命令行参数
    const args = parseCommandLineArgs();

    if (args.help) {
        showUsage();
        return;
    }

    if (!args.privateKey) {
        console.error('错误：必须提供私钥');
        console.error('使用 --help 查看使用说明');
        return;
    }

    try {
        // 验证和设置参数
        const privateKey = validatePrivateKey(args.privateKey);
        const rpcUrl = args.rpcUrl || DEFAULT_CONFIG.RPC_URL;
        const contractAddress = args.delegationContract || DEFAULT_CONFIG.DELEGATION_CONTRACT;
        const recipientAddress = args.recipient || DEFAULT_CONFIG.RECIPIENT_ADDRESS;

        console.log('\n=== 配置信息 ===');
        console.log('RPC 地址:', rpcUrl);
        console.log('委托合约:', contractAddress);
        console.log('接收地址:', recipientAddress);

        // 检查委托合约地址
        if (contractAddress === '******************************************') {
            console.error('⚠️  警告：使用的是示例委托合约地址，请部署实际合约并更新地址');
            console.error('可以使用 --contract 参数指定实际的委托合约地址');
        }

        // 初始化 provider 和 signer
        console.log('\n=== 初始化连接 ===');
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);

        console.log('钱包地址:', signer.address);

        // 检查网络信息
        const network = await provider.getNetwork();
        const balance = await provider.getBalance(signer.address);

        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');

        // 验证链 ID
        if (network.chainId !== BigInt(DEFAULT_CONFIG.CHAIN_ID)) {
            console.log(`⚠️  警告：当前链 ID (${network.chainId}) 不是 Polygon 主网 (${DEFAULT_CONFIG.CHAIN_ID})`);
        }

        // 检查余额
        if (balance < ethers.parseEther('0.01')) {
            console.error('⚠️  警告：账户余额较低，可能不足以支付 gas 费用');
        }

        // 检查 EIP-7702 支持
        const isSupported = await checkEIP7702Support(provider);
        if (!isSupported) {
            console.error('❌ 当前环境可能不支持 EIP-7702');
            return;
        }

        // 检查当前委托状态
        await checkDelegationStatus(provider, signer.address);

        // 执行交易流程
        console.log('\n=== 开始执行 EIP-7702 交易流程 ===');

        // 1. 发送非赞助交易
        const receipt1 = await sendNonSponsoredTransaction(
            signer,
            contractAddress,
            recipientAddress,
            network.chainId
        );

        // 检查委托状态
        await checkDelegationStatus(provider, signer.address);

        // 2. 如果提供了赞助方私钥，发送赞助交易
        if (args.sponsorKey) {
            console.log('\n=== 准备赞助交易 ===');
            const sponsorKey = validatePrivateKey(args.sponsorKey);
            const sponsorSigner = new ethers.Wallet(sponsorKey, provider);
            const sponsorBalance = await provider.getBalance(sponsorSigner.address);

            console.log('赞助方地址:', sponsorSigner.address);
            console.log('赞助方余额:', ethers.formatEther(sponsorBalance), 'MATIC');

            if (sponsorBalance < ethers.parseEther('0.01')) {
                console.error('⚠️  警告：赞助方余额较低，可能不足以支付 gas 费用');
            }

            const receipt2 = await sendSponsoredTransaction(
                signer,
                sponsorSigner,
                contractAddress,
                recipientAddress
            );

            console.log('\n=== 交易完成 ===');
            console.log('非赞助交易区块:', receipt1.blockNumber);
            console.log('赞助交易区块:', receipt2.blockNumber);
        } else {
            console.log('\n=== 交易完成 ===');
            console.log('非赞助交易区块:', receipt1.blockNumber);
            console.log('提示：使用 --sponsor-key 参数可以测试赞助交易');
        }

        // 询问是否撤销委托
        console.log('\n=== 可选操作 ===');
        console.log('如需撤销委托，请手动调用 revokeDelegation 函数');
        console.log('或重新运行脚本并添加撤销逻辑');

        console.log('\n✅ EIP-7702 交易测试完成');

    } catch (error) {
        handleError(error, 'EIP-7702 交易测试');
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}