/**
 * 测试 EIP-7702 委托功能
 * 修复 nonce 问题并进行完整测试
 */

const { ethers } = require('ethers');

const RPC_URL = 'https://cold-dawn-pool.matic.quiknode.pro/b3b7d9e315efb44a0165c8ebab4edc8668a2bb68/';
const PRIVATE_KEY = 'fa1931ab85f8605ae590e60e88aae8086f5aea18149535fc75d52ff5cbae78a2';

async function testEIP7702Delegation() {
    console.log('🧪 测试 EIP-7702 委托功能');
    
    try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
        
        console.log('\n=== 基本信息 ===');
        const network = await provider.getNetwork();
        const currentNonce = await provider.getTransactionCount(wallet.address);
        const balance = await provider.getBalance(wallet.address);
        
        console.log('网络:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('钱包地址:', wallet.address);
        console.log('当前 nonce:', currentNonce);
        console.log('余额:', ethers.formatEther(balance), 'MATIC');
        
        // 步骤 1: 部署一个简单的委托合约
        console.log('\n=== 步骤 1: 部署委托合约 ===');
        
        const delegationABI = [
            "constructor()",
            "function execute((address,uint256,bytes)[] calls) external payable",
            "function getValue() external view returns (uint256)",
            "function setValue(uint256 _value) external",
            "function getBalance() external view returns (uint256)"
        ];
        
        // 使用一个已知工作的简单合约字节码
        const delegationBytecode = "0x608060405234801561001057600080fd5b50610200806100206000396000f3fe608060405234801561001057600080fd5b50600436106100575760003560e01c806312fb68e01461005c57806320965255146100785780633fa4f245146100965780635524107714610b4578063d087d288146100d0575b600080fd5b610076600480360381019061007191906101a4565b6100ee565b005b610080610177565b60405161008d91906101f5565b60405180910390f35b61009e61017d565b6040516100ab91906101f5565b60405180910390f35b6100ce60048036038101906100c99190610210565b610183565b005b6100d861018d565b6040516100e591906101f5565b60405180910390f35b60005b815181101561017357600082828151811061010f5761010e61023d565b5b6020026020010151905060008160000151826020015183604001516040516101379190610278565b60006040518083038185875af1925050503d8060008114610174576040519150601f19603f3d011682016040523d82523d6000602084013e610179565b606091505b505090508061018757600080fd5b5050808061019490610295565b9150506100f1565b5050565b60005481565b4790565b8060008190555050565b60005490565b600080fd5b600080fd5b600080fd5b600080fd5b600080fd5b60008083601f8401126101c4576101c361019f565b5b8235905067ffffffffffffffff8111156101e1576101e06101a4565b5b6020830191508360208202830111156101fd576101fc6101a9565b5b9250929050565b6000819050919050565b61021781610204565b82525050565b6000602082019050610232600083018461020e565b92915050565b600060208284031215610248576102476101ae565b5b600061025684828501610227565b91505092915050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b600082516102a28184602087016102de565b9190910192915050565b60006001820161026b57634e487b7160e01b600052601160045260246000fd5b5060010190565b60005b838110156102fc5781810151838201526020016102e4565b8381111561030b576000848401525b5050505056fea26469706673582212200000000000000000000000000000000000000000000000000000000000000000064736f6c63430008110033";
        
        try {
            const factory = new ethers.ContractFactory(delegationABI, delegationBytecode, wallet);
            
            // 估算部署 gas
            const deployTx = factory.getDeployTransaction();
            const gasEstimate = await provider.estimateGas(deployTx);
            console.log('估算部署 gas:', gasEstimate.toString());
            
            const contract = await factory.deploy({
                gasLimit: gasEstimate + BigInt(50000) // 增加缓冲
            });
            
            console.log('✅ 部署交易已发送:', contract.deploymentTransaction().hash);
            
            await contract.waitForDeployment();
            const contractAddress = await contract.getAddress();
            
            console.log('✅ 委托合约部署成功:', contractAddress);
            
            // 验证合约功能
            const testValue = await contract.getValue();
            console.log('✅ 合约功能验证成功，初始值:', testValue.toString());
            
            // 步骤 2: 创建委托授权
            console.log('\n=== 步骤 2: 创建委托授权 ===');
            
            const newNonce = await provider.getTransactionCount(wallet.address);
            console.log('当前 nonce:', newNonce);
            
            const authorization = await wallet.authorize({
                address: contractAddress,
                nonce: newNonce + 1, // 下一个 nonce
                chainId: network.chainId
            });
            
            console.log('✅ 授权创建成功');
            console.log('授权地址:', authorization.address);
            console.log('授权 nonce:', authorization.nonce.toString());
            
            // 步骤 3: 发送委托交易
            console.log('\n=== 步骤 3: 发送委托交易 ===');
            
            const delegationTx = await wallet.sendTransaction({
                type: 4, // EIP-7702
                to: wallet.address,
                value: 0,
                data: '0x',
                authorizationList: [authorization],
                gasLimit: 200000
            });
            
            console.log('✅ 委托交易已发送:', delegationTx.hash);
            
            const receipt = await delegationTx.wait();
            console.log('✅ 委托交易确认成功');
            console.log('区块号:', receipt.blockNumber);
            console.log('Gas 使用量:', receipt.gasUsed.toString());
            
            // 步骤 4: 验证委托状态
            console.log('\n=== 步骤 4: 验证委托状态 ===');
            
            const walletCode = await provider.getCode(wallet.address);
            
            if (walletCode !== '0x') {
                console.log('🎉 委托成功！EOA 现在有合约代码');
                console.log('代码长度:', walletCode.length);
                console.log('代码前缀:', walletCode.substring(0, 20) + '...');
                
                // 步骤 5: 测试委托合约功能
                console.log('\n=== 步骤 5: 测试委托合约功能 ===');
                
                const delegatedContract = new ethers.Contract(wallet.address, delegationABI, wallet);
                
                try {
                    // 测试读取函数
                    const currentValue = await delegatedContract.getValue();
                    console.log('✅ 读取函数正常，当前值:', currentValue.toString());
                    
                    // 测试写入函数
                    const setTx = await delegatedContract.setValue(123);
                    await setTx.wait();
                    console.log('✅ 写入函数正常');
                    
                    const newValue = await delegatedContract.getValue();
                    console.log('✅ 新值:', newValue.toString());
                    
                    // 测试批量执行函数
                    console.log('\n=== 测试批量执行 ===');
                    
                    const calls = [
                        [wallet.address, ethers.parseEther('0.001'), '0x'],
                        [wallet.address, ethers.parseEther('0.002'), '0x']
                    ];
                    
                    const executeTx = await delegatedContract.execute(calls, {
                        value: ethers.parseEther('0.003')
                    });
                    
                    await executeTx.wait();
                    console.log('✅ 批量执行成功');
                    
                    return {
                        success: true,
                        contractAddress: contractAddress,
                        delegationTxHash: delegationTx.hash,
                        blockNumber: receipt.blockNumber
                    };
                    
                } catch (error) {
                    console.log('❌ 委托合约功能测试失败:', error.message);
                    return {
                        success: false,
                        error: 'delegation_function_failed',
                        contractAddress: contractAddress
                    };
                }
                
            } else {
                console.log('❌ 委托失败，EOA 仍然没有合约代码');
                return {
                    success: false,
                    error: 'delegation_failed',
                    contractAddress: contractAddress
                };
            }
            
        } catch (error) {
            console.log('❌ 合约部署失败:', error.message);
            return {
                success: false,
                error: 'deployment_failed'
            };
        }
        
    } catch (error) {
        console.error('测试过程发生错误:', error.message);
        return {
            success: false,
            error: 'test_failed',
            message: error.message
        };
    }
}

async function main() {
    console.log('🚀 EIP-7702 委托功能完整测试');
    
    const result = await testEIP7702Delegation();
    
    console.log('\n=== 测试结果 ===');
    
    if (result.success) {
        console.log('🎉 EIP-7702 委托功能测试成功！');
        console.log('📍 委托合约地址:', result.contractAddress);
        console.log('🔗 委托交易哈希:', result.delegationTxHash);
        console.log('📦 确认区块号:', result.blockNumber);
        console.log('');
        console.log('✅ Polygon 已完全支持 EIP-7702！');
        console.log('');
        console.log('🔧 下一步：');
        console.log('1. 更新 eip7702_test.js 中的委托合约地址');
        console.log('2. 测试赞助交易功能');
        console.log('3. 进行更复杂的批量调用测试');
        
    } else {
        console.log('❌ EIP-7702 委托功能测试失败');
        console.log('错误类型:', result.error);
        
        if (result.message) {
            console.log('错误信息:', result.message);
        }
        
        if (result.contractAddress) {
            console.log('合约地址:', result.contractAddress);
        }
        
        console.log('');
        console.log('💡 可能的原因：');
        console.log('1. EIP-7702 尚未在 Polygon 完全激活');
        console.log('2. 网络配置问题');
        console.log('3. 合约部署问题');
        console.log('4. Gas 限制问题');
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
