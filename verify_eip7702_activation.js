/**
 * 验证 Polygon 上 EIP-7702 的激活状态
 */

const { ethers } = require('ethers');

const RPC_URL = 'https://cold-dawn-pool.matic.quiknode.pro/b3b7d9e315efb44a0165c8ebab4edc8668a2bb68/';
const PRIVATE_KEY = 'fa1931ab85f8605ae590e60e88aae8086f5aea18149535fc75d52ff5cbae78a2';

async function verifyEIP7702Activation() {
    console.log('🔍 验证 Polygon 上 EIP-7702 激活状态');
    
    try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
        
        console.log('\n=== 网络信息 ===');
        const network = await provider.getNetwork();
        const blockNumber = await provider.getBlockNumber();
        
        console.log('网络:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('当前区块:', blockNumber);
        console.log('钱包地址:', wallet.address);
        
        // 测试 1: 创建授权签名
        console.log('\n=== 测试 1: 授权签名创建 ===');
        try {
            const testAuth = await wallet.authorize({
                address: '******************************************',
                nonce: await provider.getTransactionCount(wallet.address),
                chainId: network.chainId
            });
            console.log('✅ 授权签名创建成功');
            console.log('授权地址:', testAuth.address);
            console.log('Nonce:', testAuth.nonce.toString());
        } catch (error) {
            console.log('❌ 授权签名创建失败:', error.message);
            return false;
        }
        
        // 测试 2: 发送 EIP-7702 交易（撤销委托）
        console.log('\n=== 测试 2: EIP-7702 交易发送 ===');
        try {
            const currentNonce = await provider.getTransactionCount(wallet.address);
            
            const authorization = await wallet.authorize({
                address: '******************************************', // 零地址 = 撤销委托
                nonce: currentNonce + 1,
                chainId: network.chainId
            });
            
            const tx = await wallet.sendTransaction({
                type: 4, // EIP-7702
                to: wallet.address,
                value: 0,
                data: '0x',
                authorizationList: [authorization],
                gasLimit: 100000
            });
            
            console.log('✅ EIP-7702 交易发送成功');
            console.log('交易哈希:', tx.hash);
            
            // 等待确认
            const receipt = await tx.wait();
            console.log('✅ 交易确认成功');
            console.log('区块号:', receipt.blockNumber);
            console.log('Gas 使用量:', receipt.gasUsed.toString());
            
            return true;
            
        } catch (error) {
            console.log('❌ EIP-7702 交易失败:', error.message);
            
            // 分析错误类型
            if (error.message.includes('CALL_EXCEPTION')) {
                console.log('💡 这可能表示 EIP-7702 尚未完全激活');
            } else if (error.message.includes('invalid transaction type')) {
                console.log('💡 网络不支持交易类型 4 (EIP-7702)');
            } else {
                console.log('💡 其他错误，可能是网络或配置问题');
            }
            
            return false;
        }
        
    } catch (error) {
        console.error('验证过程发生错误:', error.message);
        return false;
    }
}

async function testDelegationFunctionality() {
    console.log('\n🧪 测试委托功能');
    
    try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
        
        // 部署一个简单的测试合约
        console.log('\n=== 部署测试合约 ===');
        
        // 简单的存储合约
        const contractABI = [
            "constructor()",
            "function setValue(uint256 _value) external",
            "function getValue() external view returns (uint256)",
            "function getBalance() external view returns (uint256)"
        ];
        
        // 简单合约的字节码（只有存储功能）
        const contractBytecode = "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c806320965255146100465780633fa4f2451461006257806355241077146100805780638381f58a1461009c575b600080fd5b61004e6100ba565b60405161005991906100f5565b60405180910390f35b61006a6100c0565b60405161007791906100f5565b60405180910390f35b61009a6004803603810190610095919061010e565b6100c6565b005b6100a46100d0565b6040516100b191906100f5565b60405180910390f35b60005481565b47905090565b8060008190555050565b60005490565b6000819050919050565b6100ef816100dc565b82525050565b600060208201905061010a60008301846100e6565b92915050565b600060208284031215610126576101256101e6565b5b6000610134848285016101eb565b91505092915050565b600080fd5b610151816100dc565b811461015c57600080fd5b50565b60008135905061016e81610148565b92915050565b600060208284031215610186576101856101e6565b5b60006101948482850161015f565b91505092915050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b600060028204905060018216806101e457607f821691505b602082108114156101f8576101f761019d565b5b5091905056fea26469706673582212200000000000000000000000000000000000000000000000000000000000000000064736f6c63430008110033";
        
        try {
            const factory = new ethers.ContractFactory(contractABI, contractBytecode, wallet);
            const contract = await factory.deploy();
            await contract.waitForDeployment();
            
            const contractAddress = await contract.getAddress();
            console.log('✅ 测试合约部署成功:', contractAddress);
            
            // 测试委托到这个合约
            console.log('\n=== 测试委托功能 ===');
            
            const currentNonce = await provider.getTransactionCount(wallet.address);
            
            const authorization = await wallet.authorize({
                address: contractAddress,
                nonce: currentNonce + 1,
                chainId: (await provider.getNetwork()).chainId
            });
            
            const delegationTx = await wallet.sendTransaction({
                type: 4,
                to: wallet.address,
                value: 0,
                data: '0x',
                authorizationList: [authorization],
                gasLimit: 150000
            });
            
            console.log('✅ 委托交易发送成功:', delegationTx.hash);
            
            const receipt = await delegationTx.wait();
            console.log('✅ 委托交易确认成功');
            
            // 检查委托状态
            console.log('\n=== 检查委托状态 ===');
            const code = await provider.getCode(wallet.address);
            
            if (code !== '0x') {
                console.log('🎉 委托成功！EOA 现在有合约代码');
                console.log('代码长度:', code.length);
                
                // 尝试调用委托合约的函数
                const delegatedContract = new ethers.Contract(wallet.address, contractABI, wallet);
                
                try {
                    const value = await delegatedContract.getValue();
                    console.log('✅ 可以调用委托合约函数，当前值:', value.toString());
                    
                    // 尝试设置值
                    const setTx = await delegatedContract.setValue(42);
                    await setTx.wait();
                    console.log('✅ 成功设置值');
                    
                    const newValue = await delegatedContract.getValue();
                    console.log('✅ 新值:', newValue.toString());
                    
                } catch (error) {
                    console.log('❌ 调用委托合约函数失败:', error.message);
                }
                
            } else {
                console.log('❌ 委托失败，EOA 仍然没有合约代码');
            }
            
            return contractAddress;
            
        } catch (error) {
            console.log('❌ 测试合约部署失败:', error.message);
            return null;
        }
        
    } catch (error) {
        console.error('测试委托功能时发生错误:', error.message);
        return null;
    }
}

async function main() {
    console.log('🚀 验证 Polygon EIP-7702 激活状态');
    
    // 基本验证
    const isActivated = await verifyEIP7702Activation();
    
    if (isActivated) {
        console.log('\n🎉 EIP-7702 在 Polygon 上已激活！');
        
        // 测试完整的委托功能
        const testContract = await testDelegationFunctionality();
        
        if (testContract) {
            console.log('\n✅ 完整的 EIP-7702 功能验证成功！');
            console.log('📍 测试合约地址:', testContract);
            console.log('');
            console.log('🔧 下一步建议：');
            console.log('1. 更新 eip7702_test.js 中的委托合约地址');
            console.log('2. 运行完整的批量调用测试');
            console.log('3. 测试赞助交易功能');
        }
        
    } else {
        console.log('\n⚠️  EIP-7702 可能尚未完全激活或存在其他问题');
        console.log('建议：');
        console.log('1. 检查网络配置');
        console.log('2. 确认 Polygon 的 EIP-7702 激活状态');
        console.log('3. 尝试使用其他 RPC 端点');
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
