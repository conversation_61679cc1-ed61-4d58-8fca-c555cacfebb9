/**
 * 最小化委托合约部署脚本
 * 
 * 使用最简单的合约实现，专注于 EIP-7702 测试
 */

const { ethers } = require('ethers');

// 最小化委托合约的 Solidity 源码
const MINIMAL_CONTRACT_SOURCE = `
pragma solidity ^0.8.19;

contract MinimalDelegation {
    uint256 public nonce;
    
    struct Call {
        address to;
        uint256 value;
        bytes data;
    }
    
    event BatchExecuted(address indexed executor, uint256 callCount);
    
    function execute(Call[] calldata calls) external payable {
        for (uint256 i = 0; i < calls.length; i++) {
            (bool success,) = calls[i].to.call{value: calls[i].value}(calls[i].data);
            require(success, "Call failed");
        }
        emit BatchExecuted(msg.sender, calls.length);
    }
    
    function getNonce() external view returns (uint256) {
        return nonce;
    }
    
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    receive() external payable {}
}
`;

// 对应的 ABI
const MINIMAL_CONTRACT_ABI = [
    "constructor()",
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function nonce() external view returns (uint256)",
    "function getNonce() external view returns (uint256)",
    "function getBalance() external view returns (uint256)",
    "event BatchExecuted(address indexed executor, uint256 callCount)"
];

// 手动编译的最小字节码 (只包含核心功能)
const MINIMAL_BYTECODE = "0x608060405234801561001057600080fd5b50610300806100206000396000f3fe60806040526004361061004a5760003560e01c8063affed0e01461004f57806312fb68e014610074578063d087d28814610087578063e78cea921461009c575b600080fd5b34801561005b57600080fd5b506000545b6040519081526020015b60405180910390f35b610085610082366004610180565b50565b005b34801561009357600080fd5b50476060565b3480156100a857600080fd5b506000546060565b60005b81518110156101235760008282815181106100d1576100d1610250565b6020026020010151905060008160000151826020015183604001516040516100f99190610266565b60006040518083038185875af1925050503d8060008114610136576040519150601f19603f3d011682016040523d82523d6000602084013e61013b565b606091505b505090508061014957600080fd5b505080610155906102a8565b9150506100b4565b5050565b600082601f83011261017857600080fd5b813567ffffffffffffffff8082111561019357610193610282565b604051601f8301601f19908116603f011681019082821181831017156101bb576101bb610282565b816040528381528660208588010111156101d457600080fd5b836020870160208301376000602085830101528094505050505092915050565b6000806040838503121561020757600080fd5b823567ffffffffffffffff8082111561021f57600080fd5b818501915085601f83011261023357600080fd5b813560208282111561024757610247610282565b8160051b925061025881840161019357600080fd5b828152928401810192818101908985111561027257600080fd5b948201945b8486101561029057853587529482019490820190610277565b96505086013592505080821115610298576102986102a6565b506102a585828601610167565b9150509250929050565b634e487b7160e01b600052601160045260246000fd5b6000600182016102ca576102ca610298565b5060010190565b634e487b7160e01b600052604160045260246000fd5b600082516102f88184602087016102fc565b9190910192915050565b60005b838110156103175781810151838201526020016102ff565b838111156103265750506000910152565b50505056fea26469706673582212200000000000000000000000000000000000000000000000000000000000000000064736f6c63430008130033";

/**
 * 解析命令行参数
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        privateKey: null,
        rpcUrl: null,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--private-key':
            case '--key':
                if (i + 1 < args.length) {
                    result.privateKey = args[i + 1];
                    i++;
                }
                break;
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--help':
            case '-h':
                result.help = true;
                break;
        }
    }

    return result;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('最小化委托合约部署脚本使用说明:');
    console.log('');
    console.log('基本用法:');
    console.log('  node deploy_minimal_delegation.js --private-key YOUR_PRIVATE_KEY [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --private-key <key>    部署者私钥 (必需)');
    console.log('  --rpc <url>           RPC 端点地址 (默认: Polygon 主网)');
    console.log('  --help, -h            显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node deploy_minimal_delegation.js --private-key 0x123...');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }
    
    const cleanKey = privateKey.replace(/^0x/, '');
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }
    
    return '0x' + cleanKey;
}

/**
 * 部署最小化委托合约
 */
async function deployMinimalContract(signer) {
    console.log('\n=== 开始部署最小化委托合约 ===');
    
    try {
        // 创建合约工厂
        const contractFactory = new ethers.ContractFactory(
            MINIMAL_CONTRACT_ABI,
            MINIMAL_BYTECODE,
            signer
        );
        
        // 估算 gas
        const gasEstimate = await contractFactory.getDeployTransaction().then(tx => 
            signer.provider.estimateGas(tx)
        );
        
        console.log('估算 Gas 使用量:', gasEstimate.toString());
        
        // 获取 gas 价格
        const feeData = await signer.provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        
        const estimatedCost = gasEstimate * gasPrice;
        console.log('预估部署成本:', ethers.formatEther(estimatedCost), 'MATIC');
        
        // 检查余额
        const balance = await signer.provider.getBalance(signer.address);
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        if (balance < estimatedCost) {
            throw new Error(`余额不足！需要至少 ${ethers.formatEther(estimatedCost)} MATIC`);
        }
        
        // 部署合约
        console.log('正在部署合约...');
        const contract = await contractFactory.deploy({
            gasLimit: gasEstimate + (gasEstimate * BigInt(20) / BigInt(100)) // 增加 20% 缓冲
        });
        
        console.log('✅ 合约部署交易已发送');
        console.log('交易哈希:', contract.deploymentTransaction().hash);
        console.log('合约地址:', await contract.getAddress());
        
        // 等待部署确认
        console.log('正在等待部署确认...');
        await contract.waitForDeployment();
        
        console.log('✅ 合约部署成功！');
        
        // 获取部署收据
        const receipt = await contract.deploymentTransaction().wait();
        console.log('部署区块号:', receipt.blockNumber);
        console.log('实际 Gas 使用量:', receipt.gasUsed.toString());
        console.log('实际部署成本:', ethers.formatEther(receipt.gasUsed * receipt.gasPrice), 'MATIC');
        
        // 验证合约功能
        console.log('\n=== 验证合约功能 ===');
        const initialNonce = await contract.nonce();
        console.log('初始 nonce:', initialNonce.toString());
        
        const contractBalance = await contract.getBalance();
        console.log('合约余额:', ethers.formatEther(contractBalance), 'MATIC');
        
        return {
            address: await contract.getAddress(),
            contract: contract,
            receipt: receipt
        };
        
    } catch (error) {
        console.error('部署合约失败:', error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 最小化委托合约部署脚本启动');
    
    // 解析命令行参数
    const args = parseCommandLineArgs();
    
    if (args.help) {
        showUsage();
        return;
    }
    
    if (!args.privateKey) {
        console.error('错误：必须提供私钥');
        console.error('使用 --help 查看使用说明');
        return;
    }
    
    try {
        // 验证和设置参数
        const privateKey = validatePrivateKey(args.privateKey);
        const rpcUrl = args.rpcUrl || 'https://polygon.blockpi.network/v1/rpc/3661d2d3e65b37a63f1dc8e5930f43df97385c71';
        
        console.log('\n=== 配置信息 ===');
        console.log('RPC 地址:', rpcUrl);
        
        // 初始化 provider 和 signer
        console.log('\n=== 初始化连接 ===');
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);
        
        console.log('部署者地址:', signer.address);
        
        // 检查网络信息
        const network = await provider.getNetwork();
        const balance = await provider.getBalance(signer.address);
        
        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        // 部署合约
        const deployment = await deployMinimalContract(signer);
        
        console.log('\n=== 部署完成 ===');
        console.log('✅ 最小化委托合约已成功部署到 Polygon 网络');
        console.log('📍 合约地址:', deployment.address);
        console.log('🔗 区块浏览器:', `https://polygonscan.com/address/${deployment.address}`);
        console.log('');
        console.log('💡 使用提示:');
        console.log(`   在 EIP-7702 测试脚本中使用以下地址:`);
        console.log(`   --contract ${deployment.address}`);
        console.log('');
        console.log('⚠️  注意: 这是一个最小化版本的委托合约，仅用于 EIP-7702 测试');
        
    } catch (error) {
        console.error('部署失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
