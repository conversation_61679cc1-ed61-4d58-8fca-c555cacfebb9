# BlockPI JavaScript 工具集

本项目包含多个区块链交互脚本，支持EVM链和TON区块链的交易处理。

## 🆕 EIP-7702 交易测试工具

本项目新增了完整的 EIP-7702 (Set Code Transaction) 测试工具，支持 Polygon 主网和其他兼容网络。

### 主要文件

- `eip7702_test.js` - EIP-7702 交易测试脚本
- `DelegationContract.sol` - 示例委托合约
- `deploy_delegation_contract.js` - 委托合约部署脚本
- `EIP7702_README.md` - 详细使用指南

### 快速开始

1. **安装依赖**
```bash
npm install ethers@^6.14.3
```

2. **部署委托合约** (可选)
```bash
node deploy_delegation_contract.js --private-key YOUR_PRIVATE_KEY --rpc https://polygon-rpc.com
```

3. **运行 EIP-7702 测试**
```bash
node eip7702_test.js --private-key YOUR_PRIVATE_KEY --rpc https://polygon-rpc.com
```

### ⚠️ 重要提醒

**EIP-7702 目前可能还未在 Polygon 主网完全激活**。建议：
- 先在支持的测试网络进行测试
- 关注 Polygon 官方公告了解 EIP-7702 激活状态
- 使用 `--help` 参数查看详细使用说明

### EIP-7702 功能特性

- ✅ 支持 EIP-7702 交易类型 (0x04)
- ✅ 自动构造授权列表 (authorization list)
- ✅ 支持非赞助和赞助交易模式
- ✅ 自动检测链 ID 和估算 gas 费用
- ✅ 完整的交易签名和发送流程
- ✅ 委托状态检查和撤销功能
- ✅ 详细的错误处理和日志输出

---

## 其他脚本列表

### 1. sign_tx.js —— EVM链签名脚本
基于ethers.js库的EVM链交易签名脚本，支持自动获取chainId和预估gas费用。

### 2. ton_transfer_boc.js —— TON区块链转账BOC生成脚本
基于@ton/ton库的TON区块链转账BOC(Bag of Cells)生成脚本，支持自动获取钱包信息和费用估算。默认使用TON主网。

## EVM链签名脚本功能特性

- ✅ 自动获取钱包余额
- ✅ 自动获取网络chainId
- ✅ 自动预估gas费用 (支持EIP-1559)
- ✅ 自动估算gas limit (适应合约交互)
- ✅ 支持命令行参数指定RPC地址
- ✅ 硬编码默认RPC地址作为fallback
- ✅ 硬编码配置，无需外部配置文件
- ✅ 交易签名功能
- ✅ 直接使用eth_sendRawTransaction RPC方法发送交易
- ✅ 可选择仅签名或签名并发送
- ✅ 详细的错误处理和日志输出

## TON转账BOC脚本功能特性

- ✅ 自动获取钱包余额和序列号(seqno)
- ✅ 自动估算交易费用
- ✅ 支持命令行参数指定API端点
- ✅ 硬编码默认配置作为fallback
- ✅ 硬编码配置，无需外部配置文件
- ✅ 生成签名的BOC(Bag of Cells)数据
- ✅ 可选择仅生成BOC或生成并发送

- ✅ 详细的错误处理和日志输出

## 安装依赖

```bash
npm install
```

依赖包说明:
- `ethers`: EVM链交互库 (用于sign_tx.js)
- `@ton/ton`: TON区块链核心库 (用于ton_transfer_boc.js)
- `@ton/crypto`: TON加密功能库 (用于ton_transfer_boc.js)
- `@ton/core`: TON核心数据结构库 (用于ton_transfer_boc.js)

## 配置

### EVM链脚本配置 (sign_tx.js)

#### 方式1: 修改脚本中的硬编码配置

编辑 `sign_tx.js` 文件中的 `CONFIG` 对象：

```javascript
const CONFIG = {
    // 私钥 - 请替换为你的实际私钥
    PRIVATE_KEY: '0x1234567890abcdef...',
    // 接收地址 - 请替换为实际的接收地址
    RECIPIENT_ADDRESS: '******************************************'
};
```

#### 方式2: 使用命令行参数 (推荐)

无需修改代码，直接通过命令行参数传递配置。

### TON区块链脚本配置 (ton_transfer_boc.js)

编辑 `ton_transfer_boc.js` 文件中的 `CONFIG` 对象：

```javascript
const CONFIG = {
    // TON助记词 - 请替换为你的实际助记词(24个单词)
    MNEMONIC: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon art',
    // 接收地址 - 请替换为实际的TON接收地址
    RECIPIENT_ADDRESS: 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t',
    // 转账金额 (TON)
    AMOUNT: '0.1',
    // 转账备注
    COMMENT: 'Test transfer from script'
};
```

## 使用方法

### EVM链脚本使用 (sign_tx.js)

#### 仅签名模式 (默认)
```bash
# 使用硬编码配置，仅签名不发送
node sign_tx.js

# 指定RPC地址，仅签名不发送
node sign_tx.js --rpc https://eth-mainnet.example.com
```

#### 签名并发送交易
```bash
# 使用默认RPC，签名并发送交易
node sign_tx.js --send

# 指定RPC地址，签名并发送交易
node sign_tx.js --rpc https://eth-mainnet.example.com --send
node sign_tx.js --rpc https://polygon-rpc.com --send
node sign_tx.js --rpc https://bsc-dataseed.binance.org --send
```

#### 查看帮助信息
```bash
node sign_tx.js --help
```

### TON区块链脚本使用 (ton_transfer_boc.js)

#### 仅生成BOC模式 (默认)
```bash
# 使用默认API端点，仅生成BOC不发送
node ton_transfer_boc.js

# 指定自定义API端点
node ton_transfer_boc.js --endpoint https://your-ton-api.com/jsonRPC
```

#### 生成BOC并发送交易
```bash
# 使用默认API端点，生成BOC并发送交易
node ton_transfer_boc.js --send

# 指定API端点并发送交易
node ton_transfer_boc.js --endpoint https://your-ton-api.com/jsonRPC --send
```

#### 查看帮助信息
```bash
node ton_transfer_boc.js --help
```

## 配置优先级

### RPC地址
1. **命令行参数 --rpc** (最高优先级)
2. **硬编码的默认地址**

### 私钥和接收地址
- **硬编码的配置值** (需要在脚本中修改CONFIG对象)

## Gas估算机制

脚本会自动估算交易所需的gas limit，适应不同类型的交易：

### 自动估算流程
1. **使用 `provider.estimateGas()`** 估算基础gas需求
2. **添加20%安全缓冲** 防止gas不足导致交易失败
3. **fallback机制** 如果估算失败，使用默认值21000

### 适用场景
- ✅ **简单ETH转账** - 通常21000 gas
- ✅ **合约交互** - 根据合约复杂度自动调整
- ✅ **代币转账** - ERC20等代币交易
- ✅ **复杂合约调用** - DeFi、NFT等复杂操作


## 注意事项

1. **私钥安全**:
   - 避免在命令行历史中暴露私钥
   - 建议修改脚本中的硬编码配置而不是使用命令行参数
   - 确保脚本文件不会被提交到版本控制系统
2. **测试网络**: 建议先在测试网络上测试脚本
3. **余额检查**: 脚本会自动检查余额是否足够支付gas费用
4. **网络兼容性**: 确保目标网络支持EIP-1559，否则可能需要调整gas费用设置

## 故障排除

### 常见错误

1. **配置错误**: 检查私钥和接收地址是否正确配置
2. **网络连接错误**: 检查RPC地址是否正确且可访问
3. **私钥格式错误**: 确保私钥格式正确 (64位十六进制字符串，可选0x前缀)
4. **余额不足**: 确保钱包有足够的ETH支付gas费用
5. **nonce错误**: 可能是网络拥堵，稍后重试

### 调试建议

- 使用`--help`参数查看使用说明
- 检查脚本中的CONFIG配置是否正确
- 尝试使用不同的RPC地址
- 在测试网络上先进行测试
- 使用命令行参数覆盖硬编码配置进行测试
