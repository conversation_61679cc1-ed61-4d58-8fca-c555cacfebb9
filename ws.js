import { WebSocket } from 'k6/experimental/websockets';

export const options = {
    vus: 1000,
    duration: "1m",
    iterations: 100,
  };

export default function () {
    const ws = new WebSocket('ws://localhost:10000');
    ws.binaryType = "arraybuffer";

    const url = 'wss://merlin.blockpi.network/v1/ws/9ec920efdc8675adcb65f4ea1f2578331f2009c0';

    var data = {
        "jsonrpc": "2.0",
        "method": "eth_chainId",  // eth_getBlockReceipts 400U
        "params": [],
        "id": 1
    }

    ws.onopen = () => {
        console.log('connected');
    };

    ws.onmessage = (data) => {
        console.log('a message received');
        console.log(data);
    };

    ws.onerror = (e) => {
        console.log(e);
        ws.close();
    };

    ws.onclose = () => {
        console.log('WebSocket connection closed!');
    };

    check(res, { 'status is 200': (r) => r && r.status === 200 });
    // Multiple event handlers on the same event
    // ws.addEventListener('message', () => {
    //     console.log('addEventListener event handler!');
    //     ws.close();
    // });
}