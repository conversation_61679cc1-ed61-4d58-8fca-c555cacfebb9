# EIP-7702 使用示例

本文档提供了 EIP-7702 交易测试脚本的详细使用示例。

## 前置条件

1. **安装依赖**
```bash
npm install ethers@^6.14.3
```

2. **准备测试账户**
- 主账户：用于发送 EIP-7702 交易
- 赞助账户：用于支付 gas 费用（可选）
- 确保账户有足够的 MATIC 余额

## 示例 1：基本 EIP-7702 交易

### 步骤 1：检查脚本功能
```bash
# 查看帮助信息
node eip7702_test.js --help
```

### 步骤 2：发送基本交易
```bash
# 使用默认配置发送 EIP-7702 交易
node eip7702_test.js --private-key 0x1234567890abcdef...

# 指定自定义 RPC 端点
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://polygon-mainnet.infura.io/v3/YOUR_PROJECT_ID
```

### 预期输出
```
🚀 EIP-7702 交易测试脚本启动
⚠️  注意：EIP-7702 可能还未在 Polygon 主网激活

=== 配置信息 ===
RPC 地址: https://polygon-rpc.com
委托合约: ******************************************
接收地址: ******************************************

=== 初始化连接 ===
钱包地址: 0xYourWalletAddress
网络名称: matic
链 ID: 137
账户余额: 1.5 MATIC

=== 检查委托状态: 0xYourWalletAddress ===
❌ 未发现委托，账户为普通 EOA

=== 发送非赞助 EIP-7702 交易 ===
✅ 交易已发送
交易哈希: 0x1234...
✅ 交易确认成功

✅ EIP-7702 交易测试完成
```

## 示例 2：部署自定义委托合约

### 步骤 1：部署合约
```bash
# 部署委托合约到 Polygon 主网
node deploy_delegation_contract.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://polygon-rpc.com

# 使用自定义 gas 设置
node deploy_delegation_contract.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://polygon-rpc.com \
  --gas-price 30 \
  --gas-limit 2000000
```

### 步骤 2：使用部署的合约
```bash
# 使用部署的合约地址进行测试
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --contract 0xYourDeployedContractAddress \
  --rpc https://polygon-rpc.com
```

## 示例 3：赞助交易模式

```bash
# 使用赞助方支付 gas 费用
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --sponsor-key 0xabcdef1234567890... \
  --rpc https://polygon-rpc.com \
  --contract 0xYourContractAddress
```

### 赞助交易流程说明

1. **主账户**：创建授权并签名操作
2. **赞助账户**：支付 gas 费用并发送交易
3. **执行结果**：主账户的操作被执行，但 gas 由赞助方支付

## 示例 4：测试网络使用

### Ethereum Sepolia 测试网
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://sepolia.infura.io/v3/YOUR_PROJECT_ID \
  --contract ******************************************
```

### Polygon Mumbai 测试网
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://rpc-mumbai.maticvigil.com \
  --contract 0xYourTestnetContractAddress
```

## 示例 5：完整工作流程

### 1. 部署合约
```bash
node deploy_delegation_contract.js \
  --private-key 0x1234567890abcdef... \
  --rpc https://polygon-rpc.com
```

### 2. 记录合约地址
```
✅ 委托合约已成功部署到 Polygon 网络
📍 合约地址: 0xNewContractAddress
```

### 3. 执行非赞助交易
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --contract 0xNewContractAddress \
  --rpc https://polygon-rpc.com
```

### 4. 执行赞助交易
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --sponsor-key 0xabcdef1234567890... \
  --contract 0xNewContractAddress \
  --rpc https://polygon-rpc.com
```

## 故障排除示例

### 问题 1：网络不支持 EIP-7702
```
❌ 当前环境可能不支持 EIP-7702
```

**解决方案**：
- 使用支持 EIP-7702 的测试网络
- 等待 Polygon 主网激活 EIP-7702

### 问题 2：余额不足
```
⚠️  警告：账户余额较低，可能不足以支付 gas 费用
```

**解决方案**：
```bash
# 检查账户余额
# 确保至少有 0.01 MATIC 用于 gas 费用
```

### 问题 3：合约地址无效
```
⚠️  警告：使用的是示例委托合约地址，请部署实际合约并更新地址
```

**解决方案**：
```bash
# 先部署合约
node deploy_delegation_contract.js --private-key YOUR_KEY

# 然后使用实际地址
node eip7702_test.js --private-key YOUR_KEY --contract REAL_CONTRACT_ADDRESS
```

## 高级用法

### 自定义接收地址
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --recipient 0xYourCustomRecipientAddress \
  --contract 0xYourContractAddress
```

### 组合所有参数
```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --sponsor-key 0xabcdef1234567890... \
  --rpc https://polygon-mainnet.infura.io/v3/YOUR_PROJECT_ID \
  --contract 0xYourContractAddress \
  --recipient 0xYourRecipientAddress
```

## 安全注意事项

1. **私钥管理**
   - 永远不要在公共场所暴露私钥
   - 使用环境变量或安全的密钥管理工具
   - 定期轮换测试私钥

2. **测试建议**
   - 先在测试网络验证功能
   - 使用小额资金进行测试
   - 验证合约代码的安全性

3. **网络选择**
   - 确认目标网络支持 EIP-7702
   - 检查网络的稳定性和可靠性
   - 了解网络的 gas 费用结构

## 相关资源

- [EIP-7702 规范](https://eips.ethereum.org/EIPS/eip-7702)
- [Polygon 开发者文档](https://docs.polygon.technology/)
- [ethers.js 文档](https://docs.ethers.org/)
- [EIP-7702 详细指南](./EIP7702_README.md)
