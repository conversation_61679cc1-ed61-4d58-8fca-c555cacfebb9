/**
 * 委托合约部署脚本
 * 
 * 用于部署 EIP-7702 委托合约到 Polygon 主网或测试网
 * 
 * 使用方法：
 * node deploy_delegation_contract.js --private-key YOUR_PRIVATE_KEY --rpc https://polygon-rpc.com
 */

const { ethers } = require('ethers');

// 委托合约的字节码 (需要先编译 Solidity 合约)
const DELEGATION_CONTRACT_BYTECODE = "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";

// 委托合约 ABI
const DELEGATION_CONTRACT_ABI = [
    "constructor()",
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function execute((address,uint256,bytes)[] calls, bytes signature) external payable",
    "function nonce() external view returns (uint256)",
    "function getNonce() external view returns (uint256)",
    "function getBalance() external view returns (uint256)",
    "function emergencyWithdraw() external",
    "receive() external payable",
    "fallback() external payable"
];

/**
 * 解析命令行参数
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        privateKey: null,
        rpcUrl: null,
        gasPrice: null,
        gasLimit: null,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--private-key':
            case '--key':
                if (i + 1 < args.length) {
                    result.privateKey = args[i + 1];
                    i++;
                }
                break;
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--gas-price':
                if (i + 1 < args.length) {
                    result.gasPrice = args[i + 1];
                    i++;
                }
                break;
            case '--gas-limit':
                if (i + 1 < args.length) {
                    result.gasLimit = args[i + 1];
                    i++;
                }
                break;
            case '--help':
            case '-h':
                result.help = true;
                break;
        }
    }

    return result;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('委托合约部署脚本使用说明:');
    console.log('');
    console.log('基本用法:');
    console.log('  node deploy_delegation_contract.js --private-key YOUR_PRIVATE_KEY [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --private-key <key>    部署者私钥 (必需)');
    console.log('  --rpc <url>           RPC 端点地址 (默认: Polygon 主网)');
    console.log('  --gas-price <price>   Gas 价格 (gwei)');
    console.log('  --gas-limit <limit>   Gas 限制');
    console.log('  --help, -h            显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node deploy_delegation_contract.js --private-key 0x123... --rpc https://polygon-rpc.com');
    console.log('  node deploy_delegation_contract.js --private-key 0x123... --gas-price 30 --gas-limit 2000000');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }
    
    const cleanKey = privateKey.replace(/^0x/, '');
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }
    
    return '0x' + cleanKey;
}

/**
 * 部署委托合约
 */
async function deployDelegationContract(signer, gasPrice, gasLimit) {
    console.log('\n=== 开始部署委托合约 ===');
    
    try {
        // 创建合约工厂
        const contractFactory = new ethers.ContractFactory(
            DELEGATION_CONTRACT_ABI,
            DELEGATION_CONTRACT_BYTECODE,
            signer
        );
        
        // 准备部署参数
        const deployOptions = {};
        
        if (gasPrice) {
            deployOptions.gasPrice = ethers.parseUnits(gasPrice, 'gwei');
            console.log('使用自定义 Gas 价格:', gasPrice, 'gwei');
        }
        
        if (gasLimit) {
            deployOptions.gasLimit = BigInt(gasLimit);
            console.log('使用自定义 Gas 限制:', gasLimit);
        }
        
        // 估算部署成本
        console.log('正在估算部署成本...');
        const deployTx = await contractFactory.getDeployTransaction();
        const estimatedGas = await signer.estimateGas(deployTx);
        
        console.log('估算的 Gas 使用量:', estimatedGas.toString());
        
        if (!gasLimit) {
            deployOptions.gasLimit = estimatedGas + (estimatedGas * BigInt(20) / BigInt(100)); // 增加 20% 缓冲
        }
        
        // 获取 Gas 价格
        const feeData = await signer.provider.getFeeData();
        const actualGasPrice = gasPrice ? deployOptions.gasPrice : feeData.gasPrice;
        
        const estimatedCost = deployOptions.gasLimit * actualGasPrice;
        console.log('预估部署成本:', ethers.formatEther(estimatedCost), 'MATIC');
        
        // 检查余额
        const balance = await signer.provider.getBalance(signer.address);
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        if (balance < estimatedCost) {
            throw new Error(`余额不足！需要至少 ${ethers.formatEther(estimatedCost)} MATIC`);
        }
        
        // 部署合约
        console.log('正在部署合约...');
        const contract = await contractFactory.deploy(deployOptions);
        
        console.log('✅ 合约部署交易已发送');
        console.log('交易哈希:', contract.deploymentTransaction().hash);
        console.log('合约地址:', await contract.getAddress());
        
        // 等待部署确认
        console.log('正在等待部署确认...');
        await contract.waitForDeployment();
        
        console.log('✅ 合约部署成功！');
        
        // 获取部署收据
        const receipt = await contract.deploymentTransaction().wait();
        console.log('部署区块号:', receipt.blockNumber);
        console.log('实际 Gas 使用量:', receipt.gasUsed.toString());
        console.log('实际部署成本:', ethers.formatEther(receipt.gasUsed * receipt.gasPrice), 'MATIC');
        
        // 验证合约功能
        console.log('\n=== 验证合约功能 ===');
        const initialNonce = await contract.nonce();
        console.log('初始 nonce:', initialNonce.toString());
        
        const contractBalance = await contract.getBalance();
        console.log('合约余额:', ethers.formatEther(contractBalance), 'MATIC');
        
        return {
            address: await contract.getAddress(),
            contract: contract,
            receipt: receipt
        };
        
    } catch (error) {
        console.error('部署合约失败:', error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 委托合约部署脚本启动');
    
    // 解析命令行参数
    const args = parseCommandLineArgs();
    
    if (args.help) {
        showUsage();
        return;
    }
    
    if (!args.privateKey) {
        console.error('错误：必须提供私钥');
        console.error('使用 --help 查看使用说明');
        return;
    }
    
    try {
        // 验证和设置参数
        const privateKey = validatePrivateKey(args.privateKey);
        const rpcUrl = args.rpcUrl || 'https://polygon-rpc.com';
        
        console.log('\n=== 配置信息 ===');
        console.log('RPC 地址:', rpcUrl);
        console.log('Gas 价格:', args.gasPrice ? `${args.gasPrice} gwei` : '自动');
        console.log('Gas 限制:', args.gasLimit || '自动');
        
        // 初始化 provider 和 signer
        console.log('\n=== 初始化连接 ===');
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);
        
        console.log('部署者地址:', signer.address);
        
        // 检查网络信息
        const network = await provider.getNetwork();
        const balance = await provider.getBalance(signer.address);
        
        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        // 部署合约
        const deployment = await deployDelegationContract(signer, args.gasPrice, args.gasLimit);
        
        console.log('\n=== 部署完成 ===');
        console.log('✅ 委托合约已成功部署到 Polygon 网络');
        console.log('📍 合约地址:', deployment.address);
        console.log('🔗 区块浏览器:', `https://polygonscan.com/address/${deployment.address}`);
        console.log('');
        console.log('💡 使用提示:');
        console.log(`   在 EIP-7702 测试脚本中使用以下地址:`);
        console.log(`   --contract ${deployment.address}`);
        
    } catch (error) {
        console.error('部署失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
