import http from 'k6/http';
import { check } from 'k6';

export let options = {
    duration: '5m',
    vus: 500
        // rps: 200
};

export default function() {

    const url = 'http://46.4.139.66:40731/v1'

    const params = {
        headers: {
            "Content-Type": "application/json"
        }
    };

    // const responses = http.batch([
    //     ['POST', url, blockNumber(), params],
    //     ['POST', url, estimateGas(), params],
    //     ['POST', url, getBlockByNumber(), params],
    //     ['POST', url, getLogs(), params],
    //     ['POST', url, gasPrice(), params]
    // ]);

    const requests = [
        () => eth_call(),
        () => blockNumber(),
        () => getBlockByNumber(),
        () => eth_getTransactionReceipt(),
        () => getLogs()
    ];

    // const requests = [
    //     () => eth_call(),
    //     () => estimate_gas_price(),
    //     () => get_account_resource(),
    //     () => get_events_by_event_handle(),
    //     () => check_basic_node_health()
    // ];

    // 随机选择一个请求
    const randomIndex = Math.floor(Math.random() * requests.length);
    const response = http.post(url, requests[randomIndex](), params);
    // const response = http.get(url, requests[randomIndex]());

    check(response, {
        'status 200': (r) => r.status === 200
    });

    // responses.forEach((res, index) =>
    //     check(res, {
    //         [`req${index+1} status 200`]: (r) => r.status === 200
    //     })
    // );
}

function blockNumber() {
    let payload = JSON.stringify({
        "jsonrpc": "2.0",
        "method": "eth_blockNumber",
        "params": [],
        "id": 1
    });
    return payload
}

function estimateGas() {
    let payload = JSON.stringify({
        "method": "eth_estimateGas",
        "params": [{
            "from": "******************************************",
            "to": "******************************************",
            "data": "0x"
        }],
        "id": 1,
        "jsonrpc": "2.0"
    });
    return payload
}

function getBlockByNumber() {
    let payload = JSON.stringify({
        "jsonrpc": "2.0",
        "method": "eth_getBlockByNumber",
        "params": ["latest", true],
        "id": 1
    });
    return payload
}

function getLogs() {
    let payload = JSON.stringify({
        "jsonrpc": "2.0",
        "id": 0,
        "method": "eth_getLogs",
        "params": [{
            "fromBlock": "latest",
            "toBlock": "latest"
        }]
    });
    return payload
}

function gasPrice() {
    let payload = JSON.stringify({
        "jsonrpc": "2.0",
        "method": "eth_gasPrice",
        "params": [],
        "id": 1
    });
    return payload
}

function eth_call() {
    let payload = JSON.stringify({
        "method": "eth_call",
        "params": [
            {
                "to": "******************************************",
                "data": "0x313ce567"
            },
            "latest"
        ],
        "id": 1,
        "jsonrpc": "2.0"
    });
    return payload
}

function eth_getTransactionReceipt() {
    let payload = JSON.stringify({
        "jsonrpc": "2.0",
        "method": "eth_getTransactionReceipt",
        "params": [
            "0xc647e920340376f2f5aa2b08698a42117ead20399d3f5ecd0d7495f3728589df"
        ],
        "id": 1
    });
    return payload
}


//movement
// function get_transactions() {
//     return "transactions"
// }

// function estimate_gas_price() {
//     return "estimate_gas_price"
// }

// function get_account_resource() {
//     return "accounts/0xd1126ce48bd65fb72190dbd9a6eaa65ba973f1e1664ac0cfba4db1d071fd0c36/resources"
// }

// function get_events_by_event_handle() {
//     return "accounts/0xd1126ce48bd65fb72190dbd9a6eaa65ba973f1e1664ac0cfba4db1d071fd0c36/events/0x1::account::Account/coin_register_events"
// }

// function check_basic_node_health() {
//     return "-/healthy"
// }