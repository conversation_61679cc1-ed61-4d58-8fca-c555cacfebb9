/**
 * EIP-7702 支持检查脚本
 * 
 * 用于检查当前环境是否支持 EIP-7702 功能
 * 包括 ethers.js 版本检查和基本功能测试
 */

const { ethers } = require('ethers');

/**
 * 检查 ethers.js 版本
 */
function checkEthersVersion() {
    console.log('=== ethers.js 版本检查 ===');
    console.log('当前 ethers.js 版本:', ethers.version);
    
    // 解析版本号
    const versionParts = ethers.version.split('.');
    const major = parseInt(versionParts[0]);
    const minor = parseInt(versionParts[1]);
    const patch = parseInt(versionParts[2]);
    
    // 检查是否满足最低版本要求 (6.14.3+)
    const isSupported = major > 6 || 
                       (major === 6 && minor > 14) || 
                       (major === 6 && minor === 14 && patch >= 3);
    
    if (isSupported) {
        console.log('✅ ethers.js 版本支持 EIP-7702');
    } else {
        console.log('❌ ethers.js 版本过低，需要 6.14.3 或更高版本');
        console.log('请运行: npm install ethers@^6.14.3');
        return false;
    }
    
    return true;
}

/**
 * 检查 EIP-7702 API 可用性
 */
async function checkEIP7702APIs() {
    console.log('\n=== EIP-7702 API 检查 ===');

    try {
        // 创建测试 provider 和钱包
        const provider = new ethers.JsonRpcProvider('https://polygon-rpc.com');
        const testWallet = ethers.Wallet.createRandom().connect(provider);
        console.log('✅ 钱包创建成功');

        // 检查 authorize 方法
        if (typeof testWallet.authorize === 'function') {
            console.log('✅ authorize 方法可用');
        } else {
            console.log('❌ authorize 方法不可用');
            return false;
        }

        // 尝试创建授权
        try {
            const auth = await testWallet.authorize({
                address: ethers.ZeroAddress,
                nonce: 0,
                chainId: 137  // 明确指定 chainId
            });

            console.log('✅ 授权创建成功');
            console.log('授权结构:', {
                address: auth.address,
                nonce: auth.nonce,
                chainId: auth.chainId,
                hasSignature: !!(auth.r && auth.s)
            });

        } catch (error) {
            console.log('❌ 授权创建失败:', error.message);
            // 如果是网络相关错误，但 API 存在，仍然认为支持
            if (error.message.includes('provider') || error.message.includes('network')) {
                console.log('⚠️  网络连接问题，但 API 可用');
                return true;
            }
            return false;
        }

        return true;

    } catch (error) {
        console.log('❌ EIP-7702 API 检查失败:', error.message);
        return false;
    }
}

/**
 * 检查网络连接和 RPC 支持
 */
async function checkNetworkSupport(rpcUrl) {
    console.log('\n=== 网络支持检查 ===');
    console.log('测试 RPC:', rpcUrl);
    
    try {
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        
        // 检查网络连接
        const network = await provider.getNetwork();
        console.log('✅ 网络连接成功');
        console.log('网络信息:', {
            name: network.name,
            chainId: network.chainId.toString()
        });
        
        // 检查是否支持最新的 JSON-RPC 方法
        try {
            const feeData = await provider.getFeeData();
            console.log('✅ EIP-1559 费用数据获取成功');
            console.log('费用信息:', {
                gasPrice: feeData.gasPrice ? ethers.formatUnits(feeData.gasPrice, 'gwei') + ' gwei' : 'N/A',
                maxFeePerGas: feeData.maxFeePerGas ? ethers.formatUnits(feeData.maxFeePerGas, 'gwei') + ' gwei' : 'N/A'
            });
        } catch (error) {
            console.log('⚠️  EIP-1559 费用数据获取失败:', error.message);
        }
        
        return true;
        
    } catch (error) {
        console.log('❌ 网络连接失败:', error.message);
        return false;
    }
}

/**
 * 检查交易类型支持
 */
function checkTransactionTypes() {
    console.log('\n=== 交易类型支持检查 ===');
    
    // 检查已知的交易类型
    const transactionTypes = {
        'Legacy (0)': 0,
        'EIP-2930 (1)': 1,
        'EIP-1559 (2)': 2,
        'EIP-4844 (3)': 3,
        'EIP-7702 (4)': 4
    };
    
    console.log('支持的交易类型:');
    for (const [name, type] of Object.entries(transactionTypes)) {
        console.log(`  ${name}: ${type}`);
    }
    
    // 尝试创建 EIP-7702 交易结构
    try {
        const tx = {
            type: 4,
            to: ethers.ZeroAddress,
            value: 0,
            authorizationList: []
        };
        
        console.log('✅ EIP-7702 交易结构创建成功');
        return true;
        
    } catch (error) {
        console.log('❌ EIP-7702 交易结构创建失败:', error.message);
        return false;
    }
}

/**
 * 生成环境报告
 */
function generateReport(results) {
    console.log('\n=== 环境支持报告 ===');
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('🎉 恭喜！您的环境完全支持 EIP-7702');
        console.log('');
        console.log('✅ 可以运行以下命令开始测试:');
        console.log('   node eip7702_test.js --private-key YOUR_PRIVATE_KEY');
        console.log('');
        console.log('📚 更多信息请查看:');
        console.log('   - EIP7702_README.md');
        console.log('   - EXAMPLES.md');
    } else {
        console.log('⚠️  您的环境存在一些问题，需要解决后才能使用 EIP-7702');
        console.log('');
        console.log('问题详情:');
        
        if (!results.ethersVersion) {
            console.log('❌ ethers.js 版本不支持，请升级到 6.14.3+');
        }
        
        if (!results.eip7702APIs) {
            console.log('❌ EIP-7702 API 不可用，请检查 ethers.js 版本');
        }
        
        if (!results.networkSupport) {
            console.log('❌ 网络连接失败，请检查 RPC 地址');
        }
        
        if (!results.transactionTypes) {
            console.log('❌ 交易类型支持有问题');
        }
        
        console.log('');
        console.log('🔧 建议的解决步骤:');
        console.log('1. 升级 ethers.js: npm install ethers@^6.14.3');
        console.log('2. 检查网络连接和 RPC 地址');
        console.log('3. 重新运行此检查脚本');
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🔍 EIP-7702 环境支持检查');
    console.log('');
    
    const results = {
        ethersVersion: false,
        eip7702APIs: false,
        networkSupport: false,
        transactionTypes: false
    };
    
    // 检查 ethers.js 版本
    results.ethersVersion = checkEthersVersion();
    
    // 如果版本支持，继续检查 API
    if (results.ethersVersion) {
        results.eip7702APIs = await checkEIP7702APIs();
    }
    
    // 检查网络支持 (使用默认的 Polygon RPC)
    const defaultRPC = 'https://polygon-rpc.com';
    results.networkSupport = await checkNetworkSupport(defaultRPC);
    
    // 检查交易类型
    results.transactionTypes = checkTransactionTypes();
    
    // 生成报告
    generateReport(results);
}

// 运行检查
if (require.main === module) {
    main().catch(error => {
        console.error('检查过程中发生错误:', error);
        process.exit(1);
    });
}
