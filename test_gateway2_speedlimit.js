import { check } from "k6";
import ws from "k6/ws";
import { sleep } from "k6";

export const options = {
    vus: 100,
    duration: "10m"
    // iterations: 100,
};

// export default function () {
//     const url = 'wss://ethereum.alpha.204001.xyz/v1/ws/7c69250336ec7b1b5bb99395dcd012f5598756d3';

//     // var data = {
//     //     "jsonrpc": "2.0",
//     //     "method": "eth_chainId",  // eth_getBlockReceipts 400U
//     //     "params": [],
//     //     "id": 1
//     // }

//     // var params = {}

//     const res = ws.connect(url, params, function (socket) {
//         // socket.send(JSON.stringify(data));
//         // console.log(socket.headers)
//         socket.on('open', () => {
//             console.log('Connected to WebSocket server');
//         });

//         socket.on('message', (message) => {
//             console.log('Message received: ', message);
//             // socket.close(); 
//         });
//         socket.on('close', () => console.log('disconnected'));
//         socket.on('error', function (e) {
//             // if (e.error() != 'websocket: close sent') {
//             //     console.error('An unexpected error occured: ', e.error());
//             // }
//             console.error('An unexpected error occured: ', e.error());
//         });
//         // let id = 1
//         let count = 1
//         // socket.setTimeout(() => {
//         socket.setInterval(() => { // 循环发送

//             // for (var a = 0; a < 10; a++) { // batchcall
//             //     let datas = [];
//             //     for (var i = 0; i < 5; i++) {
//             //         let item = JSON.parse(JSON.stringify(data));
//             //         datas.push(item)
//             //     };

//             //     socket.send(JSON.stringify(datas));
//             // }

//             for (var i = 0; i < 100; i++) {
//                 // data["id"] = id++
//                 console.log('Connection opened, current connections:'+ {count})
//                 count++
//                 socket.send(JSON.stringify(data));
//             }
//         }, 1000)

//         // socket.send(JSON.stringify(data));
//         // // 设置断开连接前的等待时间
//         // socket.setTimeout(function () {
//         //     // console.log('30 seconds passed, closing the socket');
//         //     socket.close()
//         // }, 59000);
//     });

//     // console.log(res);
//     // console.log(res.headers);
//     check(res, { 'status is 101': (r) => r && r.status === 101 });
// }

export default function () {
    const url = "ws://88.99.90.210:22082";  // 替换为你的 WebSocket 服务器 URL
    // const params = { tags: { my_tag: "websocket_test" } };  // 可添加标签来标记测试

    // 建立 WebSocket 连接
    const response = ws.connect(url, {}, function (socket) {
        socket.on("open", () => {
            console.log("Connection opened");

            // 发送一条测试消息
            // socket.send(JSON.stringify({ message: "Hello, Server!" }));

            // 设置接收消息的事件处理
            socket.on("message", (msg) => {
                console.log("Message received: ", msg);
            });

            // 关闭连接
            socket.setTimeout(() => {
                console.log("Closing the socket");
                socket.close();
            }, 1000000); // 设置连接保持时间
        });

        // 处理 WebSocket 错误
        socket.on("error", (e) => {
            console.log("WebSocket error:", e);
        });

        socket.on("close", () => {
            console.log("Connection closed");
        });
    });

    // 检查连接是否成功建立
    check(response, { "status is 101": (r) => r && r.status === 101 });
    sleep(1);
}