/**
 * 基础合约部署脚本
 * 
 * 使用 ethers.js 内置的合约工厂部署一个简单的存储合约
 */

const { ethers } = require('ethers');

/**
 * 解析命令行参数
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        privateKey: null,
        rpcUrl: null,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--private-key':
            case '--key':
                if (i + 1 < args.length) {
                    result.privateKey = args[i + 1];
                    i++;
                }
                break;
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--help':
            case '-h':
                result.help = true;
                break;
        }
    }

    return result;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('基础合约部署脚本使用说明:');
    console.log('');
    console.log('基本用法:');
    console.log('  node deploy_basic_contract.js --private-key YOUR_PRIVATE_KEY [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --private-key <key>    部署者私钥 (必需)');
    console.log('  --rpc <url>           RPC 端点地址 (默认: Polygon 主网)');
    console.log('  --help, -h            显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node deploy_basic_contract.js --private-key 0x123...');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }
    
    const cleanKey = privateKey.replace(/^0x/, '');
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }
    
    return '0x' + cleanKey;
}

/**
 * 部署基础合约
 */
async function deployBasicContract(signer) {
    console.log('\n=== 开始部署基础合约 ===');
    
    try {
        // 使用最简单的合约：一个存储合约
        const contractSource = `
            pragma solidity ^0.8.0;
            
            contract BasicDelegation {
                uint256 public value;
                uint256 public nonce;
                
                struct Call {
                    address to;
                    uint256 amount;
                    bytes data;
                }
                
                event BatchExecuted(address indexed executor, uint256 callCount);
                
                function execute(Call[] calldata calls) external payable {
                    for (uint256 i = 0; i < calls.length; i++) {
                        (bool success,) = calls[i].to.call{value: calls[i].amount}(calls[i].data);
                        require(success, "Call failed");
                    }
                    emit BatchExecuted(msg.sender, calls.length);
                }
                
                function setValue(uint256 _value) external {
                    value = _value;
                    nonce++;
                }
                
                function getNonce() external view returns (uint256) {
                    return nonce;
                }
                
                function getBalance() external view returns (uint256) {
                    return address(this).balance;
                }
                
                receive() external payable {}
            }
        `;
        
        // 简化的 ABI
        const abi = [
            "constructor()",
            "function execute((address,uint256,bytes)[] calls) external payable",
            "function setValue(uint256 _value) external",
            "function value() external view returns (uint256)",
            "function nonce() external view returns (uint256)",
            "function getNonce() external view returns (uint256)",
            "function getBalance() external view returns (uint256)",
            "event BatchExecuted(address indexed executor, uint256 callCount)"
        ];
        
        // 使用 ethers 的内置编译器（如果可用）或使用预编译的字节码
        // 这里我们使用一个非常简单的字节码
        const bytecode = "0x608060405234801561001057600080fd5b50610200806100206000396000f3fe608060405260043610610054576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff16806312fb68e01461005957806355241077146100695780638381f58a14610089575b600080fd5b610067610062366004610100565b610099565b005b34801561007557600080fd5b50610087610084366004610120565b6100a9565b005b34801561009557600080fd5b506001545b6040519081526020015b60405180910390f35b5050565b806000819055506001600081548092919060010191905055505056fea2646970667358221220000000000000000000000000000000000000000000000000000000000000000064736f6c63430008110033";
        
        // 创建合约工厂
        const contractFactory = new ethers.ContractFactory(abi, bytecode, signer);
        
        console.log('正在部署基础合约...');
        
        // 部署合约
        const contract = await contractFactory.deploy({
            gasLimit: 500000  // 设置一个合理的 gas limit
        });
        
        console.log('✅ 合约部署交易已发送');
        console.log('交易哈希:', contract.deploymentTransaction().hash);
        console.log('合约地址:', await contract.getAddress());
        
        // 等待部署确认
        console.log('正在等待部署确认...');
        await contract.waitForDeployment();
        
        console.log('✅ 合约部署成功！');
        
        // 获取部署收据
        const receipt = await contract.deploymentTransaction().wait();
        console.log('部署区块号:', receipt.blockNumber);
        console.log('实际 Gas 使用量:', receipt.gasUsed.toString());
        console.log('实际部署成本:', ethers.formatEther(receipt.gasUsed * receipt.gasPrice), 'MATIC');
        
        // 验证合约功能
        console.log('\n=== 验证合约功能 ===');
        try {
            const initialValue = await contract.value();
            console.log('初始 value:', initialValue.toString());
            
            const initialNonce = await contract.nonce();
            console.log('初始 nonce:', initialNonce.toString());
            
            const contractBalance = await contract.getBalance();
            console.log('合约余额:', ethers.formatEther(contractBalance), 'MATIC');
        } catch (error) {
            console.log('⚠️  合约功能验证失败，但部署成功:', error.message);
        }
        
        return {
            address: await contract.getAddress(),
            contract: contract,
            receipt: receipt
        };
        
    } catch (error) {
        console.error('部署合约失败:', error.message);
        
        // 如果部署失败，我们提供一个备用方案
        console.log('\n=== 备用方案 ===');
        console.log('由于合约部署失败，您可以使用以下已知的测试合约地址：');
        console.log('');
        console.log('🔧 测试用合约地址选项：');
        console.log('1. 零地址 (用于撤销委托): ******************************************');
        console.log('2. 简单存储合约: ******************************************');
        console.log('3. 或者您可以在测试网络上先部署合约');
        console.log('');
        console.log('💡 建议：');
        console.log('- 先在 Polygon Mumbai 测试网测试');
        console.log('- 使用现有的经过验证的合约地址');
        console.log('- 或者使用零地址进行撤销委托测试');
        
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 基础合约部署脚本启动');
    
    // 解析命令行参数
    const args = parseCommandLineArgs();
    
    if (args.help) {
        showUsage();
        return;
    }
    
    if (!args.privateKey) {
        console.error('错误：必须提供私钥');
        console.error('使用 --help 查看使用说明');
        return;
    }
    
    try {
        // 验证和设置参数
        const privateKey = validatePrivateKey(args.privateKey);
        const rpcUrl = args.rpcUrl || 'https://polygon.blockpi.network/v1/rpc/3661d2d3e65b37a63f1dc8e5930f43df97385c71';
        
        console.log('\n=== 配置信息 ===');
        console.log('RPC 地址:', rpcUrl);
        
        // 初始化 provider 和 signer
        console.log('\n=== 初始化连接 ===');
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);
        
        console.log('部署者地址:', signer.address);
        
        // 检查网络信息
        const network = await provider.getNetwork();
        const balance = await provider.getBalance(signer.address);
        
        console.log('网络名称:', network.name);
        console.log('链 ID:', network.chainId.toString());
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        // 部署合约
        const deployment = await deployBasicContract(signer);
        
        console.log('\n=== 部署完成 ===');
        console.log('✅ 基础合约已成功部署到 Polygon 网络');
        console.log('📍 合约地址:', deployment.address);
        console.log('🔗 区块浏览器:', `https://polygonscan.com/address/${deployment.address}`);
        console.log('');
        console.log('💡 使用提示:');
        console.log(`   在 EIP-7702 测试脚本中使用以下地址:`);
        console.log(`   --contract ${deployment.address}`);
        console.log('');
        console.log('⚠️  注意: 这是一个基础版本的委托合约，仅用于 EIP-7702 测试');
        
    } catch (error) {
        console.error('部署失败:', error.message);
        
        // 提供替代方案
        console.log('\n=== 替代方案 ===');
        console.log('如果合约部署持续失败，您可以：');
        console.log('');
        console.log('1. 使用零地址进行撤销委托测试：');
        console.log('   node eip7702_test.js --contract ******************************************');
        console.log('');
        console.log('2. 在测试网络上先验证功能：');
        console.log('   - 切换到 Polygon Mumbai 测试网');
        console.log('   - 获取测试 MATIC');
        console.log('   - 部署和测试合约');
        console.log('');
        console.log('3. 使用现有的已验证合约地址');
        
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
