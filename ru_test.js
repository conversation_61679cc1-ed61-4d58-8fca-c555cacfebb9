import http from "k6/http";
import { check, sleep } from "k6";

// 初始化配置选项
export let options = {
  duration: "10m", // 测试总持续时间
  vus: 1, // 虚拟用户数
  rps: 1,
  iterations: 100, // 总迭代次数
  thresholds: { // 阈值配置（性能指标标准）
      http_req_duration: ['p(99)<120000'], // XX% 的请求必须在 XX 秒内完成
      http_req_failed: ['rate<0.01']
  },
  // httpDebug: 'full'  // 记录 HTTP 请求和响应
  // stages: [  // 阶段配置
  // { duration: '30s', target: 20 }, // 斜坡上升
  // { duration: '2m', target: 50 }, // 稳定压力
  // { duration: '10s', target: 0 } // 斜坡下降
  //  ]
};

export default function () {
  const url = "https://ethereum.testnet.204001.xyz/v1/rpc/02911a63517afc1efdc3e3a938f41e1a30818dad";

  const data = {
    method: "eth_getBlockReceipts", // eth_getBlockReceipts 400U
    params: ["latest1"],
    id: 1,
    jsonrpc: "2.0",
  };

  // const data = {
  //     "jsonrpc": "2.0",
  //     "method": "eth_blockNumber",
  //     "params": [],
  //     "id": 1
  // }

  const params = {
    headers: {
      "Content-Type": "application/json",
      // insecureSkipTLSVerify: true  // 跳过 TLS 证书验证
      // "BLOCKPI-PUBLIC": "9MzDDxzOmbS9MXMKOdkReZvANL2M7Tkj"  // 模拟public请求
    },
  };
  let res = http.request("POST", url, JSON.stringify(data), params);
  check(res, {
    "status is 200": (r) => r && r.status === 200,
    response_time_ok: (r) => r.timings.duration < 120000,
  });
  // console.log(res.headers);
  // console.log(res.body);
  if (res.status != 200) {
    console.log(res.headers);
    console.log(res.body);
  }
  // console.log(res.body);
  sleep(1);
}
