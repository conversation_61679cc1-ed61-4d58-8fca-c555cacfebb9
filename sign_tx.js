
require('dotenv').config();
const ethers = require('ethers');

// 从环境变量读取配置
const CONFIG = {
    // 私钥 - 必须从环境变量读取
    PRIVATE_KEY: process.env.PRIVATE_KEY,
    // 默认接收地址
    RECIPIENT_ADDRESS: process.env.RECIPIENT_ADDRESS,
    // 默认RPC地址
    DEFAULT_RPC_URL: process.env.DEFAULT_RPC_URL
};


/**
 * 解析命令行参数 - 增强版本
 * 支持格式：
 * --rpc https://example.com
 * --to 0x123...
 * --amount 0.1
 * --send (发送交易到网络)
 */
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {
        rpcUrl: null,
        shouldSend: false,
        recipientAddress: null,
        amount: null
    };

    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--rpc':
                if (i + 1 < args.length) {
                    result.rpcUrl = args[i + 1];
                    i++;
                }
                break;
            case '--to':
                if (i + 1 < args.length) {
                    result.recipientAddress = args[i + 1];
                    i++;
                }
                break;
            case '--amount':
                if (i + 1 < args.length) {
                    result.amount = args[i + 1];
                    i++;
                }
                break;
            case '--send':
                result.shouldSend = true;
                break;
        }
    }

    return result;
}

/**
 * 获取RPC URL，优先级：命令行参数 > 硬编码默认值
 */
function getRpcUrl() {
    const cmdArgs = parseCommandLineArgs();

    // 优先使用命令行参数
    if (cmdArgs.rpcUrl) {
        console.log('使用命令行指定的RPC地址:', cmdArgs.rpcUrl);
        return cmdArgs.rpcUrl;
    }

    // 使用硬编码的默认值
    console.log('使用硬编码的默认RPC地址:', CONFIG.DEFAULT_RPC_URL);
    return CONFIG.DEFAULT_RPC_URL;
}

/**
 * 显示使用说明
 */
function showUsage() {
    console.log('使用方法:');
    console.log('  node sign_tx.js [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --rpc <url>    指定RPC地址');
    console.log('  --send         发送交易到网络 (默认只签名不发送)');
    console.log('  --help         显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node sign.js --rpc https://eth-mainnet.example.com');
    console.log('  node sign.js --rpc https://polygon-rpc.com --send');
    console.log('  node sign.js --send  # 使用默认RPC并发送交易');
    console.log('');
    console.log('注意: 请确保在.env文件中配置私钥和接收地址');
}

/**
 * 验证私钥格式
 */
function validatePrivateKey(privateKey) {
    if (!privateKey) {
        throw new Error('私钥不能为空');
    }
    
    // 移除可能的0x前缀
    const cleanKey = privateKey.replace(/^0x/, '');
    
    if (!/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
    }
    
    return '0x' + cleanKey;
}

/**
 * 验证以太坊地址格式
 */
function validateAddress(address) {
    if (!address) {
        throw new Error('地址不能为空');
    }
    
    if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
        throw new Error('地址格式无效');
    }
    
    return address.toLowerCase();
}

/**
 * 验证RPC URL格式
 */
function validateRpcUrl(url) {
    if (!url) {
        throw new Error('RPC URL不能为空');
    }
    
    try {
        new URL(url);
        return url;
    } catch {
        throw new Error('RPC URL格式无效');
    }
}

/**
 * 统一错误处理函数
 */
function handleError(error, context = '') {
    console.error(`${context}发生错误:`, error.message);
    
    switch (error.code) {
        case 'NETWORK_ERROR':
            console.error('网络连接错误，请检查RPC地址是否正确');
            break;
        case 'INVALID_ARGUMENT':
            console.error('参数错误，请检查私钥和地址格式');
            break;
        case 'INSUFFICIENT_FUNDS':
            console.error('余额不足，请检查账户余额');
            break;
        case 'NONCE_EXPIRED':
            console.error('Nonce过期，请重试');
            break;
        default:
            if (error.message.includes('insufficient funds')) {
                console.error('余额不足，请检查账户余额');
            } else {
                console.error('详细错误信息:', error);
            }
    }
    
    process.exit(1);
}

async function signTransaction() {
    // 检查是否需要显示帮助
    if (process.argv.includes('--help') || process.argv.includes('-h')) {
        showUsage();
        return;
    }

    // 检查必需的环境变量
    if (!CONFIG.PRIVATE_KEY) {
        console.error('错误: 请在.env文件中设置PRIVATE_KEY');
        console.error('创建.env文件并添加: PRIVATE_KEY=your_private_key_here');
        return;
    }

    if (!CONFIG.RECIPIENT_ADDRESS) {
        console.error('错误: 请在.env文件中设置RECIPIENT_ADDRESS');
        console.error('在.env文件中添加: RECIPIENT_ADDRESS=0x...');
        return;
    }

    // 解析命令行参数
    const cmdArgs = parseCommandLineArgs();

    // 获取配置
    const privateKey = validatePrivateKey(CONFIG.PRIVATE_KEY);
    const recipientAddress = validateAddress(CONFIG.RECIPIENT_ADDRESS);
    const rpcUrl = validateRpcUrl(getRpcUrl());
    const shouldSend = cmdArgs.shouldSend;

    try {
        console.log('正在连接到RPC节点...');
        // 连接到以太坊节点
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const wallet = new ethers.Wallet(privateKey, provider);

        console.log('钱包地址:', wallet.address);

        // 并行获取网络信息
        const { network, balance, feeData } = await getNetworkInfo(provider, wallet.address);

        console.log('Chain ID:', network.chainId.toString());

        // 检查钱包余额
        console.log('钱包余额:', ethers.formatEther(balance), 'ETH');

        // 获取建议的 Gas 费用 (EIP-1559)
        console.log('Gas费用信息:');
        console.log('Max Fee Per Gas:', ethers.formatUnits(feeData.maxFeePerGas, 'gwei'), 'Gwei');
        console.log('Max Priority Fee Per Gas:', ethers.formatUnits(feeData.maxPriorityFeePerGas, 'gwei'), 'Gwei');

        // 获取当前nonce
        const nonce = await wallet.getNonce();
        console.log('当前Nonce:', nonce);

        // 构建基础交易对象用于gas估算
        const baseTransaction = {
            to: recipientAddress,
            value: ethers.parseUnits('0', 'wei'), // 保持最小值，只做签名演示
            from: wallet.address
        };

        // 自动估算gas limit
        console.log('正在估算Gas Limit...');
        let estimatedGas;
        try {
            estimatedGas = await provider.estimateGas(baseTransaction);
            console.log('估算的Gas Limit:', estimatedGas.toString());
        } catch (error) {
            console.warn('Gas估算失败，使用默认值21000:', error.message);
            estimatedGas = BigInt(21000);
        }

        // 为安全起见，在估算值基础上增加20%的缓冲
        const gasLimit = estimatedGas + (estimatedGas * BigInt(20) / BigInt(100));
        console.log('最终Gas Limit (含20%缓冲):', gasLimit.toString());

        // 交易参数
        const transaction = {
            to: recipientAddress,
            value: ethers.parseUnits('0', 'wei'), // 保持最小值，只做签名演示
            gasLimit: gasLimit,
            type: 2, // EIP-1559 交易类型
            maxFeePerGas: feeData.maxFeePerGas,
            maxPriorityFeePerGas: feeData.maxPriorityFeePerGas,
            nonce: nonce,
            chainId: network.chainId
        };

        // 计算预估的Gas成本
        const gasCost = transaction.gasLimit * transaction.maxFeePerGas;
        const totalCost = gasCost + BigInt(transaction.value);

        console.log('预估Gas成本:', ethers.formatEther(gasCost), 'ETH');
        console.log('交易总成本:', ethers.formatEther(totalCost), 'ETH');

        // 检查余额是否足够
        if (balance < totalCost) {
            console.error('余额不足!');
            console.error('需要至少:', ethers.formatEther(totalCost), 'ETH');
            console.error('当前余额:', ethers.formatEther(balance), 'ETH');
            console.error('缺少:', ethers.formatEther(totalCost - balance), 'ETH');
            return;
        }

        console.log('正在签名交易...');
        // 签名交易
        const signedTransaction = await wallet.signTransaction(transaction);

        console.log('\n=== 交易签名成功 ===');
        console.log('签名后的交易数据:', signedTransaction);
        console.log('\n=== 交易详情 ===');
        console.log('发送方:', wallet.address);
        console.log('接收方:', wallet.address);
        console.log('金额:', ethers.formatEther(transaction.value), 'ETH');
        console.log('Gas Limit:', transaction.gasLimit);
        console.log('Chain ID:', transaction.chainId.toString());
        console.log('Nonce:', transaction.nonce);

        // 根据参数决定是否发送交易
        if (shouldSend) {
            console.log('\n正在使用eth_sendRawTransaction发送交易到网络...');

            // 直接使用eth_sendRawTransaction方法
            const txHash = await provider.send('eth_sendRawTransaction', [signedTransaction]);

            console.log('\n=== 交易发送成功 ===');
            console.log('交易哈希:', txHash);
            console.log('交易状态: 已提交到内存池');

            // 等待交易确认
            console.log('\n正在等待交易确认...');
            const receipt = await provider.waitForTransaction(txHash);

            console.log('\n=== 交易确认成功 ===');
            console.log('区块号:', receipt.blockNumber);
            console.log('区块哈希:', receipt.blockHash);
            console.log('Gas使用量:', receipt.gasUsed.toString());
            console.log('实际Gas费用:', ethers.formatEther(receipt.gasUsed * (receipt.gasPrice || receipt.effectiveGasPrice)), 'ETH');
            console.log('交易状态:', receipt.status === 1 ? '成功' : '失败');
        } else {
            console.log('\n=== 仅签名模式 ===');
            console.log('交易已签名但未发送到网络');
            console.log('如需发送交易，请添加 --send 参数');
        }

    } catch (error) {
        handleError(error, '签名交易时');
    }
}

/**
 * 并行获取网络信息
 */
async function getNetworkInfo(provider, walletAddress) {
    console.log('正在获取网络信息...');
    
    // 并行执行多个网络请求
    const [network, balance, feeData] = await Promise.all([
        provider.getNetwork(),
        provider.getBalance(walletAddress),
        provider.getFeeData()
    ]);
    
    return { network, balance, feeData };
}

signTransaction();
