import { SimpleAccountFactory__factory, EntryPoint__factory, SimpleAccount__factory } from "@account-abstraction/contracts"
import { StaticJsonRpcProvider } from "@ethersproject/providers"
import { Wallet } from "ethers"
import { arrayify, hexlify, getAddress, hexConcat } from "ethers/lib/utils.js"



const run = async () => {
    // GENERATE THE INITCODE
    const SIMPLE_ACCOUNT_FACTORY_ADDRESS = "******************************************"
    const lineaProvider = new StaticJsonRpcProvider("https://polygon-mumbai.blockpi.network/v1/rpc/2a251f21b7767a6945c2a17ae3d4cb9161e8ad0d")
    const owner = Wallet.createRandom()
    console.log("Generated wallet with private key:", owner.privateKey)
    console.log("Generated wallet with address:", owner.address)

    const simpleAccountFactory = SimpleAccountFactory__factory.connect(SIMPLE_ACCOUNT_FACTORY_ADDRESS, lineaProvider)
    const initCode = hexConcat([
        SIMPLE_ACCOUNT_FACTORY_ADDRESS,
        simpleAccountFactory.interface.encodeFunctionData("createAccount", [owner.address, 0])
    ])
    console.log(initCode)

    // CALCULATE THE SENDER ADDRESS
    const ENTRY_POINT_ADDRESS = "******************************************"

    const entryPoint = EntryPoint__factory.connect(ENTRY_POINT_ADDRESS, lineaProvider)

    const senderAddress = await entryPoint.callStatic
        .getSenderAddress(initCode)
        .then(() => {
            throw new Error("Expected getSenderAddress() to revert")
        })
        .catch((e) => {
            const data = e.message.match(/0x6ca7b806([a-fA-F\d]*)/)?.[1]
            if (!data) {
                return Promise.reject(new Error("Failed to parse revert data"))
            }
            const addr = getAddress(`0x${data.slice(24, 64)}`)
            return Promise.resolve(addr)
        })

    // GENERATE THE CALLDATA
    const to = "0xd8dA6BF26964aF9D7eEd9e03E53415D37aA96045" // vitalik
    const value = 0
    const data = "0x68656c6c6f" // "hello" encoded to to utf-8 bytes

    const simpleAccount = SimpleAccount__factory.connect(senderAddress, lineaProvider)
    const callData = simpleAccount.interface.encodeFunctionData("execute", [to, value, data])

    // FILL OUT THE REMAINING USEROPERATION VALUES
    const gasPrice = await lineaProvider.getGasPrice()

    const userOperation = {
        sender: senderAddress,
        nonce: hexlify(0),
        initCode: initCode,
        callData,
        callGasLimit: hexlify(100_000), // hardcode it for now at a high value
        verificationGasLimit: hexlify(400_000), // hardcode it for now at a high value
        preVerificationGas: hexlify(50_000), // hardcode it for now at a high value
        maxFeePerGas: hexlify(gasPrice),
        maxPriorityFeePerGas: hexlify(gasPrice),
        paymasterAndData: "0x",
        signature: "0x"
    }
    console.log(userOperation)

    // REQUEST PIMLICO VERIFYING PAYMASTER SPONSORSHIP
    const chain = "mumbai" // find the list of chain names on the Pimlico docs page
    const apiKey = "791d4709-0947-4509-8d85-45b8d437674b"

    const pimlicoEndpoint = `https://api.pimlico.io/v1/${chain}/rpc?apikey=${apiKey}`

    const pimlicoProvider = new StaticJsonRpcProvider(pimlicoEndpoint)

    const sponsorUserOperationResult = await pimlicoProvider.send("pm_sponsorUserOperation", [
        userOperation,
        {
            entryPoint: ENTRY_POINT_ADDRESS
        }
    ])

    const paymasterAndData = sponsorUserOperationResult.paymasterAndData

    userOperation.paymasterAndData = paymasterAndData
    console.log("paymasterAndData:" + userOperation.paymasterAndData)

    // SIGN THE USEROPERATION
    const signature = await owner.signMessage(arrayify(await entryPoint.getUserOpHash(userOperation)))

    userOperation.signature = signature
    console.log("signature:" + userOperation.signature)

    // SUBMIT THE USEROPERATION TO BE BUNDLED
    const userOperationHash = await pimlicoProvider.send("eth_sendUserOperation", [userOperation, ENTRY_POINT_ADDRESS])
    console.log("UserOperation hash:", userOperationHash)

    // let's also wait for the userOperation to be included, by continually querying for the receipts
    console.log("Querying for receipts...")
    let receipt = null
    while (receipt === null) {
        receipt = await pimlicoProvider.send("eth_getUserOperationReceipt", [userOperationHash])
        console.log(receipt)
    }

    const txHash = receipt.receipt.transactionHash
    const add = "https://sepolia.etherscan.io/tx/"
    console.log(`UserOperation included: ${add}${txHash}`)

}


run()
