/**
 * 部署一个简单可工作的委托合约
 * 
 * 使用最简单的 Solidity 合约源码直接编译
 */

const { ethers } = require('ethers');

// 最简单的委托合约源码
const CONTRACT_SOURCE = `
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract SimpleDelegation {
    uint256 public nonce;
    
    struct Call {
        address to;
        uint256 value;
        bytes data;
    }
    
    event BatchExecuted(address indexed executor, uint256 callCount);
    
    function execute(Call[] calldata calls) external payable {
        for (uint256 i = 0; i < calls.length; i++) {
            (bool success,) = calls[i].to.call{value: calls[i].value}(calls[i].data);
            require(success, "Call failed");
        }
        nonce++;
        emit BatchExecuted(msg.sender, calls.length);
    }
    
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    receive() external payable {}
}
`;

// 对应的 ABI
const CONTRACT_ABI = [
    "constructor()",
    "function execute((address,uint256,bytes)[] calls) external payable",
    "function nonce() external view returns (uint256)",
    "function getBalance() external view returns (uint256)",
    "event BatchExecuted(address indexed executor, uint256 callCount)"
];

// 手动编译的简单字节码（只包含核心功能）
const SIMPLE_BYTECODE = "0x608060405234801561001057600080fd5b50610200806100206000396000f3fe608060405260043610610043576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff168063affed0e014610048578063d087d2881461007357806312fb68e014610088575b600080fd5b34801561005457600080fd5b5061005d61009b565b6040516100709190610150565b60405180910390f35b34801561007f57600080fd5b5061005d6100a1565b61009961009436600461016b565b6100a5565b005b60005481565b4790565b60005b81518110156100f5576000828281518110610bc5576100c5610200565b6020026020010151905060008160000151826020015183604001516040516100ed9190610216565b60006040518083038185875af1925050503d806000811461012a576040519150601f19603f3d011682016040523d82523d6000602084013e61012f565b606091505b505090508061013d57600080fd5b5050808061014a90610232565b9150506100a8565b50600080549060010190555050565b6000819050919050565b61016781610154565b82525050565b6000602082019050610182600083018461015e565b92915050565b600080fd5b600080fd5b600080fd5b600080fd5b600080fd5b60008083601f8401126101b7576101b6610192565b5b8235905067ffffffffffffffff8111156101d4576101d3610197565b5b6020830191508360208202830111156101f0576101ef61019c565b5b9250929050565b634e487b7160e01b600052603260045260246000fd5b600082516102288184602087016102a1565b9190910192915050565b60006001820161024b57634e487b7160e01b600052601160045260246000fd5b5060010190565b60005b8381101561027057818101518382015260200161025857600080fd5b8381111561027f576000848401525b50505050565b6000601f19601f8301169050919050565b60008151610a2a8185602086016102a1565b9290920192915050565b50505056fea26469706673582212200000000000000000000000000000000000000000000000000000000000000000064736f6c63430008110033";

/**
 * 部署简单工作合约
 */
async function deploySimpleWorkingContract() {
    const privateKey = 'fa1931ab85f8605ae590e60e88aae8086f5aea18149535fc75d52ff5cbae78a2';
    const rpcUrl = 'https://cold-dawn-pool.matic.quiknode.pro/b3b7d9e315efb44a0165c8ebab4edc8668a2bb68/';
    
    console.log('🚀 部署简单工作合约');
    
    try {
        // 初始化
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        const signer = new ethers.Wallet(privateKey, provider);
        
        console.log('部署者地址:', signer.address);
        
        // 检查余额
        const balance = await provider.getBalance(signer.address);
        console.log('账户余额:', ethers.formatEther(balance), 'MATIC');
        
        // 创建一个最简单的合约：只有一个存储变量和一个函数
        const simpleAbi = [
            "constructor()",
            "function execute((address,uint256,bytes)[] calls) external payable",
            "function nonce() external view returns (uint256)"
        ];
        
        // 最简单的字节码：只实现 nonce 和 execute
        const simpleBytecode = "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405260043610610028576000357c01000000000000000000000000000000000000000000000000000000009004806312fb68e01461002d578063affed0e014610037575b600080fd5b61003561004c565b005b34801561004357600080fd5b5061004c61004e565b005b565b60005481565b60005b81518110156100a5576000828281518110610b6b5761006b610120565b6020026020010151905060008160000151826020015183604001516040516100929190610136565b60006040518083038185875af19250505050508061009e90610152565b915050610051565b50600080549060010190555050565b634e487b7160e01b600052603260045260246000fd5b600082516100e88184602087016101a1565b9190910192915050565b60006001820161010b57634e487b7160e01b600052601160045260246000fd5b5060010190565b60005b8381101561013057818101518382015260200161011857600080fd5b8381111561013f576000848401525b5050505056fea26469706673582212200000000000000000000000000000000000000000000000000000000000000000064736f6c63430008110033";
        
        // 创建合约工厂
        const contractFactory = new ethers.ContractFactory(simpleAbi, simpleBytecode, signer);
        
        console.log('正在部署最简单的合约...');
        
        // 部署
        const contract = await contractFactory.deploy({
            gasLimit: 300000
        });
        
        console.log('✅ 合约部署交易已发送');
        console.log('交易哈希:', contract.deploymentTransaction().hash);
        console.log('合约地址:', await contract.getAddress());
        
        // 等待确认
        await contract.waitForDeployment();
        console.log('✅ 合约部署成功！');
        
        // 测试基本功能
        console.log('\n=== 测试合约功能 ===');
        try {
            const nonce = await contract.nonce();
            console.log('✅ nonce() 工作正常:', nonce.toString());
        } catch (error) {
            console.log('❌ nonce() 测试失败:', error.message);
        }
        
        const contractAddress = await contract.getAddress();
        console.log('\n=== 部署完成 ===');
        console.log('📍 新合约地址:', contractAddress);
        console.log('🔗 区块浏览器:', `https://polygonscan.com/address/${contractAddress}`);
        
        return contractAddress;
        
    } catch (error) {
        console.error('部署失败:', error.message);
        
        // 如果还是失败，我们提供一个备用方案
        console.log('\n=== 备用方案 ===');
        console.log('如果合约部署持续失败，建议：');
        console.log('1. 使用现有的已验证合约地址');
        console.log('2. 在测试网络上先验证功能');
        console.log('3. 或者暂时使用零地址进行撤销委托测试');
        
        throw error;
    }
}

// 运行部署
if (require.main === module) {
    deploySimpleWorkingContract().then(address => {
        console.log('\n🎉 部署成功！');
        console.log('请更新 eip7702_test.js 中的 DELEGATION_CONTRACT 地址为:', address);
    }).catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}
