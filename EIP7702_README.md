# EIP-7702 交易测试脚本使用指南

## 概述

这个脚本实现了针对 Polygon 主网的 EIP-7702 (Set Code Transaction) 交易测试功能。EIP-7702 允许外部拥有账户 (EOA) 临时获得智能合约功能，实现账户抽象化。

## ⚠️ 重要提醒

**EIP-7702 目前可能还未在 Polygon 主网完全激活**。虽然 Polygon 正在通过 PIP-51 提案准备支持 EIP-7702，但主网支持可能还在开发中。建议：

1. 先在支持 EIP-7702 的测试网络进行测试
2. 关注 Polygon 官方公告了解 EIP-7702 激活状态
3. 当 Polygon 主网支持后，可直接使用此脚本

## 功能特性

- ✅ 支持 EIP-7702 交易类型 (0x04)
- ✅ 自动构造授权列表 (authorization list)
- ✅ 支持非赞助交易 (EOA 自付 gas)
- ✅ 支持赞助交易 (第三方付 gas)
- ✅ 自动检测链 ID 和估算 gas 费用
- ✅ 完整的交易签名和发送流程
- ✅ 委托状态检查和撤销功能
- ✅ 详细的错误处理和日志输出

## 安装依赖

确保已安装 ethers.js v6.14.3 或更高版本：

```bash
npm install ethers@^6.14.3
```

## 使用方法

### 基本用法

```bash
# 发送基本的 EIP-7702 交易
node eip7702_test.js --private-key YOUR_PRIVATE_KEY

# 指定自定义 RPC 端点
node eip7702_test.js --private-key YOUR_PRIVATE_KEY --rpc https://polygon-rpc.com

# 指定委托合约地址
node eip7702_test.js --private-key YOUR_PRIVATE_KEY --contract 0x1234...
```

### 赞助交易模式

```bash
# 使用赞助方支付 gas 费用
node eip7702_test.js --private-key YOUR_PRIVATE_KEY --sponsor-key SPONSOR_PRIVATE_KEY
```

### 完整参数示例

```bash
node eip7702_test.js \
  --private-key 0x1234567890abcdef... \
  --sponsor-key 0xabcdef1234567890... \
  --rpc https://polygon-mainnet.infura.io/v3/YOUR_PROJECT_ID \
  --contract ****************************************** \
  --recipient ******************************************
```

## 命令行参数

| 参数 | 必需 | 说明 |
|------|------|------|
| `--private-key` | ✅ | 发送方私钥 |
| `--sponsor-key` | ❌ | 赞助方私钥 (用于赞助交易) |
| `--rpc` | ❌ | RPC 端点地址 (默认: Polygon 主网) |
| `--contract` | ❌ | 委托合约地址 |
| `--recipient` | ❌ | 接收方地址 (默认: vitalik.eth) |
| `--help` | ❌ | 显示帮助信息 |

## 交易流程

### 1. 非赞助交易流程

1. **创建授权**: 使用 `currentNonce + 1` 创建授权签名
2. **构造交易**: 创建类型为 4 的 EIP-7702 交易
3. **批量执行**: 在一个交易中执行多个操作
4. **委托激活**: 交易成功后，EOA 获得智能合约功能

### 2. 赞助交易流程

1. **签名验证**: EOA 对操作进行签名授权
2. **赞助执行**: 赞助方发送交易并支付 gas
3. **批量操作**: 执行 EOA 授权的多个操作
4. **无需新授权**: 复用已有的委托关系

## 委托合约要求

脚本需要一个实现了以下接口的委托合约：

```solidity
interface IDelegationContract {
    // 非赞助执行
    function execute((address,uint256,bytes)[] calls) external payable;
    
    // 赞助执行 (需要签名验证)
    function execute((address,uint256,bytes)[] calls, bytes signature) external payable;
    
    // 获取当前 nonce
    function nonce() external view returns (uint256);
}
```

## 安全注意事项

1. **私钥安全**: 永远不要在代码中硬编码私钥
2. **测试环境**: 先在测试网络验证功能
3. **余额检查**: 确保账户有足够的 MATIC 支付 gas
4. **合约验证**: 使用经过审计的委托合约
5. **网络确认**: 确认目标网络支持 EIP-7702

## 故障排除

### 常见错误

1. **"不支持的操作"**: 网络可能不支持 EIP-7702
2. **"余额不足"**: 检查 MATIC 余额
3. **"Nonce 错误"**: 检查交易顺序和 nonce 管理
4. **"授权失败"**: 检查私钥和合约地址

### 调试建议

1. 使用 `--help` 查看完整参数说明
2. 检查网络连接和 RPC 端点状态
3. 验证私钥格式和权限
4. 查看详细的错误日志输出

## 示例输出

```
🚀 EIP-7702 交易测试脚本启动
⚠️  注意：EIP-7702 可能还未在 Polygon 主网激活

=== 配置信息 ===
RPC 地址: https://polygon-rpc.com
委托合约: ******************************************
接收地址: ******************************************

=== 初始化连接 ===
钱包地址: 0xYourWalletAddress
网络名称: matic
链 ID: 137
账户余额: 1.5 MATIC

=== 检查委托状态: 0xYourWalletAddress ===
❌ 未发现委托，账户为普通 EOA

=== 发送非赞助 EIP-7702 交易 ===
✅ 交易已发送
交易哈希: 0x1234...
✅ 交易确认成功

✅ EIP-7702 交易测试完成
```

## 相关资源

- [EIP-7702 规范](https://eips.ethereum.org/EIPS/eip-7702)
- [Polygon PIP-51 提案](https://github.com/maticnetwork/Polygon-Improvement-Proposals)
- [ethers.js 文档](https://docs.ethers.org/)
- [Polygon 开发者文档](https://docs.polygon.technology/)
